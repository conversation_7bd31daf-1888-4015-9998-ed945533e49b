"""
CommandExecutor refactorisé pour utiliser directement les commandes unifiées
"""

import numpy as np
import sys
import os
import traceback
from typing import List, Dict, Any, Optional, Tuple
from .unified_command import UnifiedCommand

# Import du décompresseur avec gestion du chemin
try:
    from backend.ai.command_decompressor import CommandDecompressor
except ImportError:
    try:
        from ..ai.command_decompressor import CommandDecompressor
    except ImportError:
        try:
            # Fallback pour les imports directs
            import sys
            import os
            sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'ai'))
            from command_decompressor import CommandDecompressor  # type: ignore
        except ImportError:
            # Dernier fallback - créer une classe vide pour éviter l'erreur
            class CommandDecompressor:
                def decompress_commands(self, commands):
                    return commands


class CommandExecutor:
    """Exécuteur de commandes d'automatisation pour les puzzles ARC - Version Unifiée"""

    def __init__(self):
        """Initialise l'exécuteur de commandes"""
        # Ne réinitialiser que si la grille n'est pas déjà configurée
        if not hasattr(self, 'grid') or self.grid is None:
            self.grid = None
            self.width = 0
            self.height = 0
        self.history = []
        self.error = None
        self.clipboard = None  # Pour les opérations COPY/CUT/PASTE
        self.clipboard_mask = None  # Masque pour les sélections spéciales

    def execute_commands(self, commands: List[str]) -> Dict[str, Any]:
        """
        Exécute une liste de commandes unifiées et retourne le résultat
        
        Args:
            commands: Liste de commandes au format unifié
            
        Returns:
            Dict contenant success, grid, width, height, history, error
        """
        self.grid = None
        self.width = 0
        self.height = 0
        self.history = []
        self.error = None
        self.clipboard = None

        for i, command in enumerate(commands):
            if not command.strip():
                continue

            try:
                if not self._execute_command(command):
                    # L'erreur est maintenant définie dans _execute_command
                    if not self.error:
                         self.error = f"Échec à la commande {i+1}: {command}"
                    break
            except Exception as e:
                import traceback
                self.error = f"Exception à la commande {i+1}: '{command}'. Erreur: {str(e)}\n{traceback.format_exc()}"
                break

        result = {
            "success": self.error is None,
            "grid": self.grid.tolist() if self.grid is not None else None,
            "width": self.width,
            "height": self.height,
            "history": self.history,
            "error": self.error
        }

        return result

    def validate_solution(self, commands: List[str], expected_output: List[List[int]]) -> Dict[str, Any]:
        """Valide une solution en exécutant les commandes et en comparant avec la sortie attendue"""
        result = self.execute_commands(commands)

        if not result["success"]:
            return result

        expected = expected_output

        # Vérifier que les dimensions correspondent
        grid_height = len(self.grid)
        grid_width = len(self.grid[0]) if grid_height > 0 else 0
        expected_height = len(expected)
        expected_width = len(expected[0]) if expected_height > 0 else 0

        if grid_height != expected_height or grid_width != expected_width:
            result["success"] = False
            result["error"] = f"Dimensions incorrectes: obtenu {grid_height}x{grid_width}, attendu {expected_height}x{expected_width}"
            return result

        # Vérifier que les valeurs correspondent
        grid_list = self.grid.tolist()
        if grid_list != expected:
            result["success"] = False
            result["error"] = "La grille générée ne correspond pas à la sortie attendue"
            return result

        return result

    def execute(self, command: UnifiedCommand) -> Dict[str, Any]:
        """
        Exécute une seule commande unifiée
        
        Args:
            command: Commande unifiée à exécuter
            
        Returns:
            Dict contenant success, grid, width, height, history, error
        """
        try:
            success = self._execute_unified_command(command)
            
            result = {
                "success": success and self.error is None,
                "grid": self.grid.tolist() if self.grid is not None else None,
                "width": self.width,
                "height": self.height,
                "history": self.history,
                "error": self.error
            }
            
            return result
            
        except Exception as e:
            self.error = f"Exception lors de l'exécution: {str(e)}"
            return {
                "success": False,
                "grid": None,
                "width": 0,
                "height": 0,
                "history": self.history,
                "error": self.error
            }

    def _execute_command(self, command: str) -> bool:
        """Exécute une commande individuelle au format unifié"""
        try:
            # Parser la commande unifiée
            unified_cmd = UnifiedCommand.parse(command)
            if not unified_cmd:
                self.error = f"Impossible de parser la commande: {command}"
                return False
            
            # Enregistrer la commande originale dans l'historique
            self.history.append(command)
            
            # Router vers la méthode appropriée
            action = unified_cmd.action
            
            if action == 'INIT':
                return self._cmd_init(unified_cmd)
            elif action == 'EDIT':
                return self._cmd_edit(unified_cmd)
            elif action == 'FILL':
                return self._cmd_fill(unified_cmd)
            elif action == 'CLEAR':
                return self._cmd_clear(unified_cmd)
            elif action == 'SURROUND':
                return self._cmd_surround(unified_cmd)
            elif action == 'REPLACE':
                return self._cmd_replace(unified_cmd)
            elif action == 'FLOODFILL':
                return self._cmd_floodfill(unified_cmd)
            elif action == 'CUT':
                return self._cmd_cut(unified_cmd)
            elif action == 'COPY':
                return self._cmd_copy(unified_cmd)
            elif action == 'PASTE':
                return self._cmd_paste(unified_cmd)
            elif action == 'FLIP':
                return self._cmd_flip(unified_cmd)
            elif action == 'ROTATE':
                return self._cmd_rotate(unified_cmd)
            elif action == 'INSERT':
                return self._cmd_insert(unified_cmd)
            elif action == 'DELETE':
                return self._cmd_delete(unified_cmd)
            elif action == 'EXTRACT':
                return self._cmd_extract(unified_cmd)
            elif action == 'TRANSFERT':
                return self._cmd_transfert(unified_cmd)
            elif action == 'RESIZE':
                return self._cmd_resize(unified_cmd)
            # elif action == 'PROPOSE': obsolète
            #     return self._cmd_propose(unified_cmd)
            elif action == 'END':
                return self._cmd_end(unified_cmd)
            elif action == 'SELECT_RELEASE':
                return self._cmd_select_release(unified_cmd)
            # elif action == 'EDITS':  Regroupement de commandes ce n'est pas une commandes
            #     return self._cmd_edits(unified_cmd)
            else:
                self.error = f"Commande inconnue: {action}"
                return False
                
        except Exception as e:
            import traceback
            self.error = f"Exception inattendue lors de l'exécution de '{command}': {str(e)}\n{traceback.format_exc()}"
            return False

    def _execute_unified_command(self, unified_cmd: UnifiedCommand) -> bool:
        return True
        """Exécute une commande unifiée déjà parsée"""
        try:
            # Enregistrer la commande dans l'historique
            self.history.append(str(unified_cmd))
            
            # Router vers la méthode appropriée
            action = unified_cmd.action
            
            if action == 'INIT':
                return self._cmd_init(unified_cmd)
            elif action == 'EDIT':
                return self._cmd_edit(unified_cmd)
            elif action == 'FILL':
                return self._cmd_fill(unified_cmd)
            elif action == 'CLEAR':
                return self._cmd_clear(unified_cmd)
            elif action == 'SURROUND':
                return self._cmd_surround(unified_cmd)
            elif action == 'REPLACE':
                return self._cmd_replace(unified_cmd)
            elif action == 'COPY':
                return self._cmd_copy(unified_cmd)
            elif action == 'CUT':
                return self._cmd_cut(unified_cmd)
            elif action == 'PASTE':
                return self._cmd_paste(unified_cmd)
            elif action == 'FLIP':
                return self._cmd_flip(unified_cmd)
            elif action == 'ROTATE':
                return self._cmd_rotate(unified_cmd)
            elif action == 'INSERT':
                return self._cmd_insert(unified_cmd)
            elif action == 'DELETE':
                return self._cmd_delete(unified_cmd)
            elif action == 'EXTRACT':
                return self._cmd_extract(unified_cmd)
            elif action == 'TRANSFERT':
                return self._cmd_transfert(unified_cmd)
            elif action == 'RESIZE':
                return self._cmd_resize(unified_cmd)
            # elif action == 'PROPOSE':
            #     return self._cmd_propose(unified_cmd)
            elif action == 'END':
                return self._cmd_end(unified_cmd)
            elif action == 'SELECT_RELEASE':
                return self._cmd_select_release(unified_cmd)
            else:
                self.error = f"Commande inconnue: {action}"
                return False
                
        except Exception as e:
            self.error = f"Erreur lors de l'exécution de {unified_cmd.action}: {str(e)}"
            return False

    # === COMMANDES DE BASE ===

    def _cmd_init(self, cmd: UnifiedCommand) -> bool:
        """Initialise une grille de taille width x height"""
        if len(cmd.parameters) != 2:
            return False

        try:
            width = int(cmd.parameters[0])
            height = int(cmd.parameters[1])

            if width <= 0 or height <= 0:
                return False

            # Si une grille d'entrée existe déjà avec les bonnes dimensions, la préserver
            if (hasattr(self, 'grid') and self.grid is not None and 
                hasattr(self, 'width') and hasattr(self, 'height') and
                self.width == width and self.height == height and
                self.grid.shape == (width, height)):
                # Grille déjà initialisée avec les bonnes dimensions, ne pas l'écraser
                return True

            self.width = width
            self.height = height
            # CORRECTION: INIT 19x24 signifie 19 colonnes x 24 lignes
            # numpy utilise (rows, cols) donc pour width=19, height=24 -> (height, width)
            self.grid = np.zeros((height, width), dtype=int)
            return True
        except (ValueError, TypeError):
            return False

    def _cmd_edit(self, cmd: UnifiedCommand) -> bool:
        """Modifie une ou plusieurs cellules avec une valeur"""
        if len(cmd.parameters) != 1:
            return False

        if self.grid is None:
            self.error = "La grille n'a pas été initialisée."
            return False

        try:
            value = int(cmd.parameters[0])
            
            # Utiliser la fonction générique pour parser les blocs de coordonnées
            if hasattr(cmd, 'raw_command') and cmd.raw_command:
                coordinate_blocks = self._parse_coordinate_blocks(cmd.raw_command)
            else:
                # Fallback: ancienne méthode par paires de coordonnées
                coords = cmd.coordinates
                coordinate_blocks = []
                
                if len(coords) == 2:
                    # Format simple: EDIT 1 [x,y]
                    coordinate_blocks.append([f"{coords[0]},{coords[1]}"])
                elif len(coords) % 2 == 0:
                    # Format multiple: EDIT 1 [x1,y1] [x2,y2] [x3,y3] ...
                    for i in range(0, len(coords), 2):
                        coordinate_blocks.append([f"{coords[i]},{coords[i+1]}"])
                else:
                    self.error = f"Format de coordonnées non valide pour EDIT: {cmd.coordinates}"
                    return False
            
            #print(f"[EDIT] Traitement de {len(coordinate_blocks)} blocs de coordonnées: {coordinate_blocks}")
            
            # Utiliser la fonction générique pour traiter les blocs
            def edit_action(x1, y1, x2, y2):
                # Pour EDIT, traiter chaque cellule individuellement
                for x in range(x1, x2 + 1):
                    for y in range(y1, y2 + 1):
                        self.grid[x, y] = value
                
                # if x1 == x2 and y1 == y2:
                #     print(f"[EDIT] Cellule éditée: ({x1},{y1}) = {value}")
                # else:
                #     print(f"[EDIT] Rectangle édité: ({x1},{y1}) -> ({x2},{y2}) = {value}")
            
            self._process_coordinate_blocks(coordinate_blocks, edit_action)
            return True
                
        except (ValueError, TypeError, IndexError) as e:
            self.error = f"Erreur dans _cmd_edit: {e}"
            return False

    def _cmd_fill(self, cmd: UnifiedCommand) -> bool:
        """Remplit les zones sélectionnées avec une couleur"""
        if len(cmd.parameters) != 1:
            return False

        if self.grid is None:
            return False

        try:
            color = int(cmd.parameters[0])
            
            # Utiliser la fonction générique pour parser les blocs de coordonnées
            if hasattr(cmd, 'raw_command') and cmd.raw_command:
                coordinate_blocks = self._parse_coordinate_blocks(cmd.raw_command)
            else:
                # Fallback: ancienne méthode par paires de 4 coordonnées
                coords = cmd.coordinates
                coordinate_blocks = []
                i = 0
                while i + 3 < len(coords):
                    x1, y1, x2, y2 = coords[i], coords[i+1], coords[i+2], coords[i+3]
                    if x1 == x2 and y1 == y2:
                        # Cellule simple
                        coordinate_blocks.append([f"{x1},{y1}"])
                    else:
                        # Rectangle
                        coordinate_blocks.append([f"{x1},{y1}", f"{x2},{y2}"])
                    i += 4
            
            #print(f"[FILL] Traitement de {len(coordinate_blocks)} blocs de coordonnées: {coordinate_blocks}")
            
            # Utiliser la fonction générique pour traiter les blocs
            def fill_action(x1, y1, x2, y2):
                self.grid[x1:x2+1, y1:y2+1] = color
                # if x1 == x2 and y1 == y2:
                #     print(f"[FILL] Cellule remplie: ({x1},{y1}) avec couleur {color}")
                # else:
                #     print(f"[FILL] Rectangle rempli: ({x1},{y1}) -> ({x2},{y2}) avec couleur {color}")
            
            self._process_coordinate_blocks(coordinate_blocks, fill_action)
            return True
            
        except (ValueError, TypeError, IndexError) as e:
            print(f"[FILL] Erreur: {e}")
            return False

    def _cmd_clear(self, cmd: UnifiedCommand) -> bool:
        """Efface les zones sélectionnées (remplit avec 0)"""
        if self.grid is None:
            return False

        try:
            
            # Utiliser la fonction générique pour parser les blocs de coordonnées
            if hasattr(cmd, 'raw_command') and cmd.raw_command:
                coordinate_blocks = self._parse_coordinate_blocks(cmd.raw_command)
            else:
                # Fallback: ancienne méthode par paires de 4 coordonnées
                coords = cmd.coordinates
                coordinate_blocks = []
                i = 0
                while i + 3 < len(coords):
                    x1, y1, x2, y2 = coords[i], coords[i+1], coords[i+2], coords[i+3]
                    if x1 == x2 and y1 == y2:
                        # Cellule simple
                        coordinate_blocks.append([f"{x1},{y1}"])
                    else:
                        # Rectangle
                        coordinate_blocks.append([f"{x1},{y1}", f"{x2},{y2}"])
                    i += 4
            
            #print(f"[CLEAR] Traitement de {len(coordinate_blocks)} blocs de coordonnées: {coordinate_blocks}")
            
            # Utiliser la fonction générique pour traiter les blocs
            def clear_action(x1, y1, x2, y2):
                self.grid[x1:x2+1, y1:y2+1] = 0
                # if x1 == x2 and y1 == y2:
                #     print(f"[CLEAR] Cellule effacée: ({x1},{y1})")
                # else:
                #     print(f"[CLEAR] Rectangle effacé: ({x1},{y1}) -> ({x2},{y2})")
            
            self._process_coordinate_blocks(coordinate_blocks, clear_action)
            return True
            
        except (ValueError, TypeError, IndexError) as e:
            print(f"[CLEAR] Erreur: {e}")
            return False

    def _cmd_surround(self, cmd: UnifiedCommand) -> bool:
        """Entoure les zones sélectionnées avec une couleur"""
        if len(cmd.parameters) != 1:
            return False

        if self.grid is None:
            return False

        try:
            color = int(cmd.parameters[0])
            
            # Utiliser la fonction générique pour parser les blocs de coordonnées
            if hasattr(cmd, 'raw_command') and cmd.raw_command:
                coordinate_blocks = self._parse_coordinate_blocks(cmd.raw_command)
            else:
                # Fallback: ancienne méthode par paires de 4 coordonnées
                coords = cmd.coordinates
                coordinate_blocks = []
                if cmd.coordinates:
                    # Vérification du format des coordonnées existant
                    if isinstance(cmd.coordinates[0], list):
                        # Format [[x1,y1,x2,y2]] ou [[x,y]]
                        for selection in cmd.coordinates:
                            if len(selection) == 2:
                                x, y = selection
                                coordinate_blocks.append([f"{x},{y}"])
                            elif len(selection) == 4:
                                x1, y1, x2, y2 = selection
                                if x1 == x2 and y1 == y2:
                                    coordinate_blocks.append([f"{x1},{y1}"])
                                else:
                                    coordinate_blocks.append([f"{x1},{y1}", f"{x2},{y2}"])
                    else:
                        # Format plat [x1,y1,x2,y2,...]
                        i = 0
                        while i + 3 < len(coords):
                            x1, y1, x2, y2 = coords[i], coords[i+1], coords[i+2], coords[i+3]
                            if x1 == x2 and y1 == y2:
                                coordinate_blocks.append([f"{x1},{y1}"])
                            else:
                                coordinate_blocks.append([f"{x1},{y1}", f"{x2},{y2}"])
                            i += 4
            
            #print(f"[SURROUND] Traitement de {len(coordinate_blocks)} blocs de coordonnées: {coordinate_blocks}")
            
            # Utiliser la fonction générique pour traiter les blocs
            def surround_action(x1, y1, x2, y2):
                x_min, x_max = min(x1, x2), max(x1, x2)
                y_min, y_max = min(y1, y2), max(y1, y2)

                # Coordonnées de la boîte englobante pour l'entourage
                surround_x_min = max(0, x_min - 1)
                surround_x_max = min(self.height - 1, x_max + 1)
                surround_y_min = max(0, y_min - 1)
                surround_y_max = min(self.width - 1, y_max + 1)

                # Itérer sur la boîte d'entourage et colorier si c'est une bordure
                for r in range(surround_x_min, surround_x_max + 1):
                    for c in range(surround_y_min, surround_y_max + 1):
                        # Ne pas colorier l'intérieur de la sélection originale
                        if not (x_min <= r <= x_max and y_min <= c <= y_max):
                            self.grid[r, c] = color
                
                # if x1 == x2 and y1 == y2:
                #     print(f"[SURROUND] Cellule entourée: ({x1},{y1}) avec couleur {color}")
                # else:
                #     print(f"[SURROUND] Rectangle entouré: ({x1},{y1}) -> ({x2},{y2}) avec couleur {color}")
            
            self._process_coordinate_blocks(coordinate_blocks, surround_action)
            return True
            
        except (ValueError, TypeError, IndexError) as e:
            print(f"[SURROUND] Erreur: {e}")
            return False

    def _cmd_replace(self, cmd: UnifiedCommand) -> bool:
        """Remplace des couleurs par une autre dans les zones sélectionnées"""
        if len(cmd.parameters) < 2:
            return False

        if self.grid is None:
            return False

        try:
            # Gestion des couleurs multiples pour REPLACE
            if len(cmd.parameters) >= 2:
                # Les paramètres peuvent être dans deux formats :
                # Format simple: [7, 5, 0] -> source_colors=[7, 5], target_color=0
                # Format groupé: [[7, 5], 0] -> source_colors=[7, 5], target_color=0
                
                if isinstance(cmd.parameters[0], list):
                    # Format groupé: [[7, 5], 0]
                    source_colors = cmd.parameters[0]
                    target_color = int(cmd.parameters[1])
                else:
                    # Format simple: [7, 5, 0]
                    source_colors = [int(c) for c in cmd.parameters[:-1]]
                    target_color = int(cmd.parameters[-1])
            else:
                return False
            
            # Utiliser la fonction générique pour parser les blocs de coordonnées
            if hasattr(cmd, 'raw_command') and cmd.raw_command:
                coordinate_blocks = self._parse_coordinate_blocks(cmd.raw_command)
            else:
                # Fallback: ancienne méthode
                coordinate_blocks = []
                coords_source = getattr(cmd, 'coords', None) or getattr(cmd, 'coordinates', [])
                
                if coords_source:
                    if isinstance(coords_source[0], list):
                        # Format [[x1,y1,x2,y2]]
                        for coord_group in coords_source:
                            if len(coord_group) == 4:
                                x1, y1, x2, y2 = coord_group
                                if x1 == x2 and y1 == y2:
                                    coordinate_blocks.append([f"{x1},{y1}"])
                                else:
                                    coordinate_blocks.append([f"{x1},{y1}", f"{x2},{y2}"])
                    else:
                        # Format plat [x1,y1,x2,y2,...]
                        i = 0
                        while i + 3 < len(coords_source):
                            x1, y1, x2, y2 = coords_source[i], coords_source[i+1], coords_source[i+2], coords_source[i+3]
                            if x1 == x2 and y1 == y2:
                                coordinate_blocks.append([f"{x1},{y1}"])
                            else:
                                coordinate_blocks.append([f"{x1},{y1}", f"{x2},{y2}"])
                            i += 4
            
            # print(f"[REPLACE] Traitement de {len(coordinate_blocks)} blocs de coordonnées: {coordinate_blocks}")
            # print(f"[REPLACE] Remplacement de {source_colors} par {target_color}")
            
            # Utiliser la fonction générique pour traiter les blocs
            def replace_action(x1, y1, x2, y2):
                # Accès direct à la grille pour éviter les problèmes de vues NumPy
                region = self.grid[x1:x2+1, y1:y2+1]
                mask = np.isin(region, source_colors)
                # Application directe sur self.grid
                self.grid[x1:x2+1, y1:y2+1][mask] = target_color
                
                # if x1 == x2 and y1 == y2:
                #     print(f"[REPLACE] Cellule traitée: ({x1},{y1})")
                # else:
                #     print(f"[REPLACE] Rectangle traité: ({x1},{y1}) -> ({x2},{y2}), cellules modifiées: {np.sum(mask)}")
            
            self._process_coordinate_blocks(coordinate_blocks, replace_action)
            return True
            
        except (ValueError, TypeError, IndexError) as e:
            print(f"[REPLACE] Erreur: {e}")
            return False

    # === COMMANDES PRESSE-PAPIER ===

    def _cmd_cut(self, cmd: UnifiedCommand) -> bool:
        """Coupe un rectangle ou une sélection spéciale et le place dans le presse-papier"""
        if self._cmd_copy(cmd):
            # Effacer la zone copiée en utilisant le parsing générique
            # Utiliser la fonction générique pour parser les blocs de coordonnées
            if hasattr(cmd, 'raw_command') and cmd.raw_command:
                coordinate_blocks = self._parse_coordinate_blocks(cmd.raw_command)
            else:
                # Fallback: ancienne méthode par paires de 4 coordonnées
                coords = cmd.coordinates
                coordinate_blocks = []
                i = 0
                while i + 3 < len(coords):
                    x1, y1, x2, y2 = coords[i], coords[i+1], coords[i+2], coords[i+3]
                    if x1 == x2 and y1 == y2:
                        # Cellule simple
                        coordinate_blocks.append([f"{x1},{y1}"])
                    else:
                        # Rectangle
                        coordinate_blocks.append([f"{x1},{y1}", f"{x2},{y2}"])
                    i += 4
            
            # Utiliser la fonction générique pour traiter les blocs
            def cut_action(x1, y1, x2, y2):
                self.grid[x1:x2+1, y1:y2+1] = 0
            
            self._process_coordinate_blocks(coordinate_blocks, cut_action)
            return True
        return False

    def _cmd_flip(self, unified_cmd: UnifiedCommand) -> bool:
        """Effectue une opération FLIP sur la sélection (copiée au préalable) ou sur la grille entière."""
        if not unified_cmd.parameters:
            self.error = "La commande FLIP nécessite une direction (HORIZONTAL ou VERTICAL)"
            return False

        direction = unified_cmd.parameters[0].upper()

        # Si aucune coordonnée, on flip la grille entière
        if not unified_cmd.coordinates:
            self.clipboard = self.grid.copy()
            self.clipboard_mask = None
        # Sinon, on s'attend à ce que COPY ait déjà été appelé
        elif self.clipboard is None:
            self.error = "FLIP sur une sélection nécessite une commande COPY préalable."
            return False

        if direction == 'HORIZONTAL':
            self._flip_horizontal(unified_cmd)
        elif direction == 'VERTICAL':
            self._flip_vertical(unified_cmd)
        else:
            self.error = f"Direction invalide pour FLIP: {direction}"
            return False

        # Si on a flippé la grille entière, on la met à jour
        if not unified_cmd.coordinates:
            self.grid = self.clipboard.copy()

        return True

    def _cmd_rotate(self, unified_cmd: UnifiedCommand) -> bool:
        """Effectue une opération ROTATE sur la sélection (copiée au préalable) ou sur la grille entière."""
        if not unified_cmd.parameters:
            self.error = "La commande ROTATE nécessite une direction (LEFT ou RIGHT)"
            return False

        direction = unified_cmd.parameters[0].upper()

        if not unified_cmd.coordinates:
            self.clipboard = self.grid.copy()
            self.clipboard_mask = None
        elif self.clipboard is None:
            self.error = "ROTATE sur une sélection nécessite une commande COPY préalable."
            return False

        if direction == 'LEFT':
            self._rotate_left(unified_cmd)
        elif direction == 'RIGHT':
            self._rotate_right(unified_cmd)
        else:
            self.error = f"Direction invalide pour ROTATE: {direction}"
            return False

        if not unified_cmd.coordinates:
            self.grid = self.clipboard.copy()
            self.height, self.width = self.grid.shape

        return True

    def _flip_horizontal(self):
        """Flips the clipboard horizontally."""
        if self.clipboard is not None:
            self.clipboard = np.fliplr(self.clipboard)
            if self.clipboard_mask is not None:
                self.clipboard_mask = np.fliplr(self.clipboard_mask)

    def _flip_vertical(self):
        """Flips the clipboard vertically."""
        if self.clipboard is not None:
            self.clipboard = np.flipud(self.clipboard)
            if self.clipboard_mask is not None:
                self.clipboard_mask = np.flipud(self.clipboard_mask)

    def _rotate_left(self):
        """Rotates the clipboard 90 degrees to the left."""
        if self.clipboard is not None:
            self.clipboard = np.rot90(self.clipboard)
            if self.clipboard_mask is not None:
                self.clipboard_mask = np.rot90(self.clipboard_mask)

    def _rotate_right(self):
        """Rotates the clipboard 90 degrees to the right."""
        if self.clipboard is not None:
            self.clipboard = np.rot90(self.clipboard, k=-1)
            if self.clipboard_mask is not None:
                self.clipboard_mask = np.rot90(self.clipboard_mask, k=-1)
        print(f"[ROTATE] _rotate_right: ", self.clipboard, self.clipboard_mask) 

    def _cmd_copy(self, cmd: UnifiedCommand) -> bool:
        """Copie une ou plusieurs sélections dans le presse-papier en tant que motif unifié."""
        if self.grid is None:
            self.error = "La grille n'a pas été initialisée."
            return False

        try:
            # Utiliser uniquement la fonction générique pour parser les blocs de coordonnées
            coordinate_blocks = self._parse_coordinate_blocks(cmd.raw_command) if hasattr(cmd, 'raw_command') and cmd.raw_command else []

            # Si aucun bloc de coordonnées n'est trouvé, essayer les coordonnées standards
            if not coordinate_blocks and cmd.coordinates:
                # Fallback: traiter les coordonnées par paires de 4
                coords = cmd.coordinates
                coordinate_blocks = []
                i = 0
                while i + 3 < len(coords):
                    x1, y1, x2, y2 = coords[i], coords[i+1], coords[i+2], coords[i+3]
                    if x1 == x2 and y1 == y2:
                        # Cellule simple
                        coordinate_blocks.append([f"{x1},{y1}"])
                    else:
                        # Rectangle
                        coordinate_blocks.append([f"{x1},{y1}", f"{x2},{y2}"])
                    i += 4

            # Traitement unifié des blocs avec modificateurs
            selected_cells = []
            min_x, min_y = float('inf'), float('inf')
            max_x, max_y = -1, -1

            def copy_action(x1, y1, x2, y2):
                nonlocal min_x, min_y, max_x, max_y
                min_x, min_y = min(min_x, x1), min(min_y, y1)
                max_x, max_y = max(max_x, x2), max(max_y, y2)
                
                for x in range(x1, x2 + 1):
                    for y in range(y1, y2 + 1):
                        selected_cells.append((x, y, self.grid[x, y]))

            # Utiliser la fonction générique pour traiter les blocs
            self._process_coordinate_blocks(coordinate_blocks, copy_action)

            if not selected_cells:
                self.error = "Aucune cellule à copier"
                return False

            # Créer le presse-papier avec les dimensions minimales
            clip_height = max_x - min_x + 1
            clip_width = max_y - min_y + 1
            self.clipboard = np.zeros((clip_height, clip_width), dtype=int)
            self.clipboard_mask = np.zeros((clip_height, clip_width), dtype=bool)

            # Remplir le presse-papier avec les cellules sélectionnées
            for x, y, value in selected_cells:
                rel_x, rel_y = x - min_x, y - min_y
                self.clipboard[rel_x, rel_y] = value
                self.clipboard_mask[rel_x, rel_y] = True

            return True
        except (ValueError, TypeError, IndexError) as e:
            self.error = f"Exception dans _cmd_copy: {e}"
            return False


    def _cmd_paste(self, cmd: UnifiedCommand) -> bool:
        """Colle le contenu du presse-papier."""
        if self.grid is None or self.clipboard is None:
            self.error = "La grille n'est pas initialisée ou le presse-papiers est vide."
            return False

        try:
            # Si aucune coordonnée n'est fournie, on ne fait rien (le collage doit être explicite)
            if not cmd.coordinates:
                self.error = "La commande PASTE nécessite au moins une coordonnée de destination."
                return False

            # Traiter les coordonnées par paires (x, y)
            for i in range(0, len(cmd.coordinates), 2):
                x_dest, y_dest = cmd.coordinates[i], cmd.coordinates[i+1]

                clip_height, clip_width = self.clipboard.shape

                # Vérifier les limites de la destination
                if x_dest < 0 or y_dest < 0 or x_dest >= self.height or y_dest >= self.width:
                    self.error = f"Coordonnée de collage ({x_dest},{y_dest}) hors limites."
                    return False

                # Coller en respectant le masque
                for r in range(clip_height):
                    for c in range(clip_width):
                        if self.clipboard_mask is None or self.clipboard_mask[r, c]:
                            grid_r, grid_c = x_dest + r, y_dest + c
                            if 0 <= grid_r < self.height and 0 <= grid_c < self.width:
                                self.grid[grid_r, grid_c] = self.clipboard[r, c]
            return True
        except (ValueError, TypeError, IndexError) as e:
            self.error = f"Exception dans _cmd_paste: {e}"
            return False

    def _cmd_edits(self, cmd: UnifiedCommand) -> bool:
        """Décompresse et exécute une commande EDITS groupée"""
        try:
            # Le contenu des accolades est stocké dans parameters[0]
            if not cmd.parameters or len(cmd.parameters) == 0:
                return False
                
            content = cmd.parameters[0]
            original_command = f"EDITS {{{content}}}"
            
            # Décompresser la commande EDITS
            decompressor = CommandDecompressor()
            decompressed_commands = decompressor.decompress_commands([original_command])
            
            # Exécuter chaque commande EDIT décompressée
            for edit_cmd in decompressed_commands:
                if not self._execute_command(edit_cmd):
                    return False
            
            return True
        except Exception as e:
            print(f"Erreur lors de la décompression EDITS: {e}")
            return False

    # === COMMANDES DE TRANSFORMATION ===

    def _cmd_flip(self, cmd: UnifiedCommand) -> bool:
        """Effectue une opération FLIP HORIZONTAL ou VERTICAL sur une zone"""
        if not cmd.parameters or len(cmd.parameters) != 1:
            self.error = "La commande FLIP nécessite une direction (HORIZONTAL ou VERTICAL)"
            return False

        direction = cmd.parameters[0].upper()
        if direction == 'HORIZONTAL':
            return self._flip_horizontal(cmd)
        elif direction == 'VERTICAL':
            return self._flip_vertical(cmd)
        else:
            self.error = f"Direction de FLIP non valide: {direction}"
            return False

    def _flip_horizontal(self, cmd: UnifiedCommand) -> bool:
        """Retourne horizontalement le contenu du presse-papier uniquement"""
        if self.grid is None:
            return False
        
        
        # FLIP s'applique toujours au presse-papier, peu importe les coordonnées
        if self.clipboard is not None and self.clipboard_mask is not None:
            # Retournement horizontal du presse-papier
            self.clipboard = np.fliplr(self.clipboard)
            self.clipboard_mask = np.fliplr(self.clipboard_mask)
            return True
        
        # Si pas de presse-papier, ne rien faire
        return False

    def _flip_vertical(self, cmd: UnifiedCommand) -> bool:
        """Retourne verticalement le contenu du presse-papier uniquement"""
        if self.grid is None:
            return False
        
        
        # FLIP s'applique toujours au presse-papier, peu importe les coordonnées
        if self.clipboard is not None and self.clipboard_mask is not None:
            # Retournement vertical du presse-papier
            self.clipboard = np.flipud(self.clipboard)
            self.clipboard_mask = np.flipud(self.clipboard_mask)
            return True
        
        # Si pas de presse-papier, ne rien faire
        return False

    def _rotate_left(self, cmd: UnifiedCommand) -> bool:
        """Fait tourner à gauche les zones sélectionnées ou le presse-papier"""
        if self.grid is None:
            return False
        
        
        # Détecter le contexte : presse-papier ou grille
        if self.clipboard is not None and self.clipboard_mask is not None:
            # Vérifier si nous devons agir sur le presse-papier
            if len(cmd.coordinates) >= 4:
                coords = cmd.coordinates
                x1, y1, x2, y2 = coords[0], coords[1], coords[2], coords[3]
                
                # Normaliser les coordonnées
                min_x, max_x = min(x1, x2), max(x1, x2)
                min_y, max_y = min(y1, y2), max(y1, y2)
                
                # Vérifier si la zone est majoritairement vide (indique un CUT précédent)
                if (min_y < self.grid.shape[0] and max_y < self.grid.shape[0] and 
                    min_x < self.grid.shape[1] and max_x < self.grid.shape[1]):
                    zone = self.grid[min_y:max_y+1, min_x:max_x+1]
                    empty_cells = np.sum(zone == 0)
                    total_cells = zone.size
                    
                    if total_cells > 0 and empty_cells / total_cells > 0.8:  # Plus de 80% vide
                        # Rotation du presse-papier
                        self.clipboard = np.rot90(self.clipboard, k=1)  # k=1 pour rotation antihoraire
                        self.clipboard_mask = np.rot90(self.clipboard_mask, k=1)
                        return True
            else:
                # Pas de zone spécifiée, rotation du presse-papier entier
                self.clipboard = np.rot90(self.clipboard, k=1)
                self.clipboard_mask = np.rot90(self.clipboard_mask, k=1)
                return True
        
        
        # Rotation sur la grille (comportement original)
        if len(cmd.coordinates) == 0:
            try:
                self.grid = np.rot90(self.grid)
                return True
            except Exception:
                return False
        
        if len(cmd.coordinates) < 4:
            return False

        try:
            # Traiter toutes les sélections
            coords = cmd.coordinates
            i = 0
            while i + 3 < len(coords):
                x1, y1, x2, y2 = coords[i], coords[i+1], coords[i+2], coords[i+3]
                
                # S'assurer que x1 <= x2 et y1 <= y2
                x1, x2 = min(x1, x2), max(x1, x2)
                y1, y2 = min(y1, y2), max(y1, y2)
                
                # Vérifier les limites
                if x1 < 0 or x2 >= self.height or y1 < 0 or y2 >= self.width:
                    return False
                
                # Extraire la zone
                zone = self.grid[x1:x2+1, y1:y2+1].copy()
                
                # Rotation de la zone
                zone_rotated = np.rot90(zone)
                
                # Nettoyer la zone originale
                self.grid[x1:x2+1, y1:y2+1] = 0
                
                # Replacer la zone tournée (si les dimensions le permettent)
                new_height, new_width = zone_rotated.shape
                if (x1 + new_height <= self.height and y1 + new_width <= self.width):
                    self.grid[x1:x1+new_height, y1:y1+new_width] = zone_rotated
                else:
                    return False
                
                i += 4
            
            return True
        except (ValueError, TypeError, IndexError):
            return False

    def _rotate_right(self, cmd: UnifiedCommand) -> bool:
        """Fait tourner à droite les zones sélectionnées ou le presse-papier"""
        if self.grid is None:
            return False
        
        # Détecter le contexte : presse-papier ou grille
        if self.clipboard is not None and self.clipboard_mask is not None:
            # Vérifier si nous devons agir sur le presse-papier
            if len(cmd.coordinates) >= 4:
                coords = cmd.coordinates
                x1, y1, x2, y2 = coords[0], coords[1], coords[2], coords[3]
                
                # Normaliser les coordonnées
                min_x, max_x = min(x1, x2), max(x1, x2)
                min_y, max_y = min(y1, y2), max(y1, y2)
                
                # Vérifier si la zone est majoritairement vide (indique un CUT précédent)
                if (min_y < self.grid.shape[0] and max_y < self.grid.shape[0] and 
                    min_x < self.grid.shape[1] and max_x < self.grid.shape[1]):
                    zone = self.grid[min_y:max_y+1, min_x:max_x+1]
                    empty_cells = np.sum(zone == 0)
                    total_cells = zone.size
                    
                    if total_cells > 0 and empty_cells / total_cells > 0.8:  # Plus de 80% vide
                        # Rotation du presse-papier
                        self.clipboard = np.rot90(self.clipboard, k=-1)  # k=-1 pour rotation horaire
                        self.clipboard_mask = np.rot90(self.clipboard_mask, k=-1)
                        return True
            else:
                # Pas de zone spécifiée, rotation du presse-papier entier
                self.clipboard = np.rot90(self.clipboard, k=-1)
                self.clipboard_mask = np.rot90(self.clipboard_mask, k=-1)
                return True
        
        # Rotation sur la grille (comportement original)
        if len(cmd.coordinates) == 0:
            try:
                self.grid = np.rot90(self.grid, -1)
                return True
            except Exception:
                return False
        
        if len(cmd.coordinates) < 4:
            return False

        try:
            # Traiter toutes les sélections
            coords = cmd.coordinates
            i = 0
            while i + 3 < len(coords):
                x1, y1, x2, y2 = coords[i], coords[i+1], coords[i+2], coords[i+3]
                
                # S'assurer que x1 <= x2 et y1 <= y2
                x1, x2 = min(x1, x2), max(x1, x2)
                y1, y2 = min(y1, y2), max(y1, y2)
                
                # Vérifier les limites
                if x1 < 0 or x2 >= self.height or y1 < 0 or y2 >= self.width:
                    return False
                
                # Extraire la zone
                zone = self.grid[x1:x2+1, y1:y2+1].copy()
                
                # Rotation de la zone
                zone_rotated = np.rot90(zone, k=-1)
                
                # Nettoyer la zone originale
                self.grid[x1:x2+1, y1:y2+1] = 0
                
                # Replacer la zone tournée (si les dimensions le permettent)
                new_height, new_width = zone_rotated.shape
                if (x1 + new_height <= self.height and y1 + new_width <= self.width):
                    self.grid[x1:x1+new_height, y1:y1+new_width] = zone_rotated
                else:
                    return False
                
                i += 4
            
            return True
        except (ValueError, TypeError, IndexError):
            return False

    # === COMMANDES STRUCTURELLES ===

    def _cmd_insert(self, cmd: UnifiedCommand) -> bool:
        """Insère des lignes ou colonnes"""
        # INSERT nombre ROWS/COLUMNS BEFORE/AFTER/ABOVE/BELOW [coordinates]
        if len(cmd.parameters) < 2:
            self.error = "INSERT nécessite au moins 2 paramètres: nombre et type (ROWS/COLUMNS)"
            return False

        if self.grid is None:
            self.error = "La grille n'a pas été initialisée."
            return False

        try:
            count = int(cmd.parameters[0])
            element_type = cmd.parameters[1].upper()

            if count <= 0:
                self.error = f"Le nombre d'éléments à insérer doit être positif: {count}"
                return False

            if element_type not in ['ROWS', 'COLUMNS']:
                self.error = f"Type d'élément non valide: {element_type}. Utilisez ROWS ou COLUMNS"
                return False

            # Position par défaut si non spécifiée
            position = 'AFTER' if len(cmd.parameters) < 3 else cmd.parameters[2].upper()

            if position not in ['BEFORE', 'AFTER', 'ABOVE', 'BELOW']:
                self.error = f"Position non valide: {position}. Utilisez BEFORE, AFTER, ABOVE ou BELOW"
                return False

            # Normaliser les positions pour les lignes et colonnes
            if element_type == 'ROWS':
                if position in ['BEFORE', 'ABOVE']:
                    position = 'ABOVE'
                elif position in ['AFTER', 'BELOW']:
                    position = 'BELOW'
            elif element_type == 'COLUMNS':
                if position in ['BEFORE', 'LEFT']:
                    position = 'BEFORE'
                elif position in ['AFTER', 'RIGHT']:
                    position = 'AFTER'

            # Si des coordonnées sont spécifiées, insérer à ces positions
            if cmd.coordinates:
                return self._insert_at_coordinates(count, element_type, position, cmd.coordinates)
            else:
                # Insérer au début ou à la fin selon la position
                return self._insert_global(count, element_type, position)

        except (ValueError, TypeError, IndexError) as e:
            self.error = f"Erreur dans _cmd_insert: {e}"
            return False

    def _cmd_delete(self, cmd: UnifiedCommand) -> bool:
        """Supprime des lignes ou colonnes"""
        if len(cmd.parameters) < 1:
            self.error = "DELETE nécessite au moins 1 paramètre: type (ROWS/COLUMNS)"
            return False

        if self.grid is None:
            self.error = "La grille n'a pas été initialisée."
            return False

        try:
            element_type = cmd.parameters[0].upper()

            if element_type not in ['ROWS', 'COLUMNS']:
                self.error = f"Type d'élément non valide: {element_type}. Utilisez ROWS ou COLUMNS"
                return False

            # Si des coordonnées sont spécifiées, supprimer ces lignes/colonnes spécifiques
            if cmd.coordinates:
                return self._delete_at_coordinates(element_type, cmd.coordinates)
            else:
                self.error = "DELETE nécessite des coordonnées pour spécifier quoi supprimer"
                return False

        except (ValueError, TypeError, IndexError) as e:
            self.error = f"Erreur dans _cmd_delete: {e}"
            return False

    def _insert_at_coordinates(self, count: int, element_type: str, position: str, coordinates: List[int]) -> bool:
        """Insère des lignes/colonnes aux positions spécifiées par les coordonnées"""
        try:
            # Utiliser la fonction générique pour parser les blocs de coordonnées
            if hasattr(self, '_parse_coordinate_blocks'):
                # Construire une commande temporaire pour utiliser le parser
                temp_command = f"TEMP [{','.join(map(str, coordinates))}]"
                coordinate_blocks = self._parse_coordinate_blocks(temp_command)
            else:
                # Fallback: traiter les coordonnées par paires de 4
                coordinate_blocks = []
                i = 0
                while i + 3 < len(coordinates):
                    x1, y1, x2, y2 = coordinates[i], coordinates[i+1], coordinates[i+2], coordinates[i+3]
                    coordinate_blocks.append([f"{x1},{y1}", f"{x2},{y2}"])
                    i += 4

            # Collecter toutes les positions d'insertion
            insert_positions = set()

            for block in coordinate_blocks:
                if len(block) == 1:
                    # Cellule simple
                    x, y = map(int, block[0].split(','))
                    if element_type == 'ROWS':
                        insert_positions.add(x if position == 'ABOVE' else x + 1)
                    else:  # COLUMNS
                        insert_positions.add(y if position == 'BEFORE' else y + 1)
                elif len(block) == 2:
                    # Rectangle
                    x1, y1 = map(int, block[0].split(','))
                    x2, y2 = map(int, block[1].split(','))

                    if element_type == 'ROWS':
                        if position == 'ABOVE':
                            insert_positions.add(min(x1, x2))
                        else:  # BELOW
                            insert_positions.add(max(x1, x2) + 1)
                    else:  # COLUMNS
                        if position == 'BEFORE':
                            insert_positions.add(min(y1, y2))
                        else:  # AFTER
                            insert_positions.add(max(y1, y2) + 1)

            # Trier les positions en ordre décroissant pour éviter les décalages
            sorted_positions = sorted(insert_positions, reverse=True)

            # Insérer à chaque position
            for pos in sorted_positions:
                if not self._insert_single(count, element_type, pos):
                    return False

            return True

        except (ValueError, TypeError, IndexError) as e:
            self.error = f"Erreur dans _insert_at_coordinates: {e}"
            return False

    def _insert_global(self, count: int, element_type: str, position: str) -> bool:
        """Insère des lignes/colonnes au début ou à la fin de la grille"""
        try:
            if element_type == 'ROWS':
                insert_pos = 0 if position == 'ABOVE' else self.height
            else:  # COLUMNS
                insert_pos = 0 if position == 'BEFORE' else self.width

            return self._insert_single(count, element_type, insert_pos)

        except Exception as e:
            self.error = f"Erreur dans _insert_global: {e}"
            return False

    def _insert_single(self, count: int, element_type: str, position: int) -> bool:
        """Insère count lignes/colonnes à la position spécifiée"""
        try:
            if element_type == 'ROWS':
                # Vérifier les limites
                if position < 0 or position > self.height:
                    self.error = f"Position d'insertion de ligne invalide: {position}"
                    return False

                # Créer une nouvelle grille avec plus de lignes
                new_height = self.height + count
                new_grid = np.zeros((new_height, self.width), dtype=int)

                # Copier les lignes avant la position d'insertion
                if position > 0:
                    new_grid[:position, :] = self.grid[:position, :]

                # Copier les lignes après la position d'insertion (décalées)
                if position < self.height:
                    new_grid[position + count:, :] = self.grid[position:, :]

                # Mettre à jour la grille et les dimensions
                self.grid = new_grid
                self.height = new_height

            else:  # COLUMNS
                # Vérifier les limites
                if position < 0 or position > self.width:
                    self.error = f"Position d'insertion de colonne invalide: {position}"
                    return False

                # Créer une nouvelle grille avec plus de colonnes
                new_width = self.width + count
                new_grid = np.zeros((self.height, new_width), dtype=int)

                # Copier les colonnes avant la position d'insertion
                if position > 0:
                    new_grid[:, :position] = self.grid[:, :position]

                # Copier les colonnes après la position d'insertion (décalées)
                if position < self.width:
                    new_grid[:, position + count:] = self.grid[:, position:]

                # Mettre à jour la grille et les dimensions
                self.grid = new_grid
                self.width = new_width

            return True

        except Exception as e:
            self.error = f"Erreur dans _insert_single: {e}"
            return False

    def _delete_at_coordinates(self, element_type: str, coordinates: List[int]) -> bool:
        """Supprime des lignes/colonnes aux positions spécifiées par les coordonnées"""
        try:
            # Traiter les coordonnées directement par paires de 4 (x1, y1, x2, y2)
            delete_positions = set()

            # Traiter les coordonnées par groupes de 4
            i = 0
            while i + 3 < len(coordinates):
                x1, y1, x2, y2 = coordinates[i], coordinates[i+1], coordinates[i+2], coordinates[i+3]

                if element_type == 'ROWS':
                    # Pour les lignes, utiliser les coordonnées x (lignes)
                    for row in range(min(x1, x2), max(x1, x2) + 1):
                        delete_positions.add(row)
                else:  # COLUMNS
                    # Pour les colonnes, utiliser les coordonnées y (colonnes)
                    for col in range(min(y1, y2), max(y1, y2) + 1):
                        delete_positions.add(col)

                i += 4

            # Si il reste des coordonnées (pas un multiple de 4), traiter comme des cellules simples
            while i + 1 < len(coordinates):
                x, y = coordinates[i], coordinates[i+1]
                if element_type == 'ROWS':
                    delete_positions.add(x)
                else:  # COLUMNS
                    delete_positions.add(y)
                i += 2

            # Vérifier que les positions sont valides
            if element_type == 'ROWS':
                invalid_positions = [pos for pos in delete_positions if pos < 0 or pos >= self.height]
                if invalid_positions:
                    self.error = f"Positions de lignes invalides: {invalid_positions}"
                    return False

                # Vérifier qu'on ne supprime pas toutes les lignes
                if len(delete_positions) >= self.height:
                    self.error = "Impossible de supprimer toutes les lignes de la grille"
                    return False

            else:  # COLUMNS
                invalid_positions = [pos for pos in delete_positions if pos < 0 or pos >= self.width]
                if invalid_positions:
                    self.error = f"Positions de colonnes invalides: {invalid_positions}"
                    return False

                # Vérifier qu'on ne supprime pas toutes les colonnes
                if len(delete_positions) >= self.width:
                    self.error = "Impossible de supprimer toutes les colonnes de la grille"
                    return False

            # Trier les positions en ordre décroissant pour éviter les décalages
            sorted_positions = sorted(delete_positions, reverse=True)

            # Supprimer chaque position
            for pos in sorted_positions:
                if not self._delete_single(element_type, pos):
                    return False

            return True

        except (ValueError, TypeError, IndexError) as e:
            self.error = f"Erreur dans _delete_at_coordinates: {e}"
            return False

    def _delete_single(self, element_type: str, position: int) -> bool:
        """Supprime une seule ligne/colonne à la position spécifiée"""
        try:
            if element_type == 'ROWS':
                # Vérifier les limites
                if position < 0 or position >= self.height:
                    self.error = f"Position de ligne invalide: {position}"
                    return False

                if self.height <= 1:
                    self.error = "Impossible de supprimer la dernière ligne"
                    return False

                # Créer une nouvelle grille sans cette ligne
                new_height = self.height - 1
                new_grid = np.zeros((new_height, self.width), dtype=int)

                # Copier les lignes avant la position
                if position > 0:
                    new_grid[:position, :] = self.grid[:position, :]

                # Copier les lignes après la position
                if position < self.height - 1:
                    new_grid[position:, :] = self.grid[position + 1:, :]

                # Mettre à jour la grille et les dimensions
                self.grid = new_grid
                self.height = new_height

            else:  # COLUMNS
                # Vérifier les limites
                if position < 0 or position >= self.width:
                    self.error = f"Position de colonne invalide: {position}"
                    return False

                if self.width <= 1:
                    self.error = "Impossible de supprimer la dernière colonne"
                    return False

                # Créer une nouvelle grille sans cette colonne
                new_width = self.width - 1
                new_grid = np.zeros((self.height, new_width), dtype=int)

                # Copier les colonnes avant la position
                if position > 0:
                    new_grid[:, :position] = self.grid[:, :position]

                # Copier les colonnes après la position
                if position < self.width - 1:
                    new_grid[:, position:] = self.grid[:, position + 1:]

                # Mettre à jour la grille et les dimensions
                self.grid = new_grid
                self.width = new_width

            return True

        except Exception as e:
            self.error = f"Erreur dans _delete_single: {e}"
            return False

    def _cmd_extract(self, cmd: UnifiedCommand) -> bool:
        """Extrait une zone de la grille"""
        if len(cmd.coordinates) != 4:
            return False

        if self.grid is None:
            return False

        try:
            x1, y1, x2, y2 = cmd.coordinates
            
            # S'assurer que x1 <= x2 et y1 <= y2
            x1, x2 = min(x1, x2), max(x1, x2)
            y1, y2 = min(y1, y2), max(y1, y2)
            
            # Vérifier les limites
            if x1 < 0 or x2 >= self.height or y1 < 0 or y2 >= self.width:
                return False
            
            # Extraire la zone
            extracted = self.grid[x1:x2+1, y1:y2+1].copy()
            
            # Remplacer la grille par la zone extraite
            self.grid = extracted
            self.height, self.width = extracted.shape
            
            return True
        except (ValueError, TypeError, IndexError):
            return False

    # === COMMANDES DE SÉLECTION SPÉCIALES ===


    # === COMMANDES SYSTÈME ===

    def _cmd_transfert(self, cmd: UnifiedCommand) -> bool:
        """Traite une commande TRANSFERT avec contenu"""
        if len(cmd.parameters) != 1:
            return False

        try:
            # Le contenu des accolades est dans parameters[0]
            content = cmd.parameters[0]
            
            # Séparer les commandes individuelles
            commands = [c.strip() for c in content.split(';') if c.strip()]
            
            # Exécuter chaque commande
            for sub_command in commands:
                if not self._execute_command(sub_command):
                    return False
            
            return True
        except Exception:
            return False

    def _cmd_resize(self, cmd: UnifiedCommand) -> bool:
        """Redimensionne la grille"""
        if len(cmd.parameters) != 1:
            return False

        try:
            size_str = str(cmd.parameters[0])
            if 'x' in size_str:
                width, height = map(int, size_str.split('x'))
            else:
                return False
                # # Supposer format "width height"
                # parts = size_str.split()
                # if len(parts) == 2:
                #     width, height = int(parts[0]), int(parts[1])
                # else:
                #     return False

            if width <= 0 or height <= 0:
                return False

            # Créer une nouvelle grille
            new_grid = np.zeros((height, width), dtype=int)
            
            # Copier l'ancienne grille si elle existe
            if self.grid is not None:
                min_h = min(self.height, height)
                min_w = min(self.width, width)
                new_grid[:min_h, :min_w] = self.grid[:min_h, :min_w]
                
                # Mettre à jour la grille et les dimensions
                self.grid = new_grid
                self.width = width
                self.height = height

            #print(f"[RESIZE] Redimensionnement de {self.height}x{self.width} à {height}x{width}") 
            
            return True

        except (ValueError, TypeError):
            print('ValueError, TypeError', ValueError, TypeError)
            return False

    def _cmd_floodfill(self, cmd: UnifiedCommand) -> bool:
        """Remplit une zone connectée avec une couleur donnée"""
        if len(cmd.parameters) != 1:
            return False

        if self.grid is None:
            return False

        try:
            new_color = int(cmd.parameters[0])
            
            # Utiliser la fonction générique pour parser les blocs de coordonnées
            if hasattr(cmd, 'raw_command') and cmd.raw_command:
                coordinate_blocks = self._parse_coordinate_blocks(cmd.raw_command)
            else:
                # Fallback: traiter les coordonnées par paires (x, y)
                coords = cmd.coordinates
                coordinate_blocks = []
                i = 0
                while i + 1 < len(coords):
                    x, y = coords[i], coords[i+1]
                    coordinate_blocks.append([f"{x},{y}"])
                    i += 2
            
            #print(f"[FLOODFILL] Traitement de {len(coordinate_blocks)} points de départ: {coordinate_blocks}")
            
            # Pour le flood fill, on traite chaque cellule comme point de départ
            for block in coordinate_blocks:
                if len(block) == 1:
                    # Point de départ simple
                    try:
                        coord_parts = block[0].split(',')
                        if len(coord_parts) == 2:
                            x, y = int(coord_parts[0]), int(coord_parts[1])
                            if (0 <= x < self.height and 0 <= y < self.width):
                                self._flood_fill_from_point(x, y, new_color)
                    except (ValueError, IndexError):
                        continue
                elif len(block) == 2:
                    # Rectangle : flood fill à partir de chaque cellule du rectangle
                    try:
                        coord1_parts = block[0].split(',')
                        coord2_parts = block[1].split(',')
                        if len(coord1_parts) == 2 and len(coord2_parts) == 2:
                            x1, y1 = int(coord1_parts[0]), int(coord1_parts[1])
                            x2, y2 = int(coord2_parts[0]), int(coord2_parts[1])
                            
                            if (0 <= x1 < self.height and 0 <= x2 < self.height and
                                0 <= y1 < self.width and 0 <= y2 < self.width):
                                # Normaliser les coordonnées
                                min_x, max_x = min(x1, x2), max(x1, x2)
                                min_y, max_y = min(y1, y2), max(y1, y2)
                                
                                # Flood fill à partir de chaque cellule du rectangle
                                for x in range(min_x, max_x + 1):
                                    for y in range(min_y, max_y + 1):
                                        self._flood_fill_from_point(x, y, new_color)
                    except (ValueError, IndexError):
                        continue
            
            return True
            
        except (ValueError, TypeError, IndexError) as e:
            print(f"[FLOODFILL] Erreur: {e}")
            return False

    def _flood_fill_from_point(self, start_x: int, start_y: int, new_color: int):
        """Effectue un flood fill à partir d'un point donné"""
        # Couleur d'origine
        original_color = self.grid[start_x, start_y]
        
        # Si la couleur est déjà la bonne, ne rien faire
        if original_color == new_color:
            return
        
        #print(f"[FLOODFILL] Début flood fill à partir de ({start_x},{start_y}): {original_color} -> {new_color}")
        
        # Algorithme de flood fill avec une queue
        queue = [(start_x, start_y)]
        visited = set()
        
        while queue:
            cur_x, cur_y = queue.pop(0)
            
            # Vérifier si déjà visité ou hors limites
            if (cur_x, cur_y) in visited:
                continue
            if cur_x < 0 or cur_x >= self.height or cur_y < 0 or cur_y >= self.width:
                continue
            if self.grid[cur_x, cur_y] != original_color:
                continue
            
            # Marquer comme visité et changer la couleur
            visited.add((cur_x, cur_y))
            self.grid[cur_x, cur_y] = new_color
            
            # Ajouter les voisins à la queue
            queue.append((cur_x + 1, cur_y))  # Bas
            queue.append((cur_x - 1, cur_y))  # Haut
            queue.append((cur_x, cur_y + 1))  # Droite
            queue.append((cur_x, cur_y - 1))  # Gauche

    def _flip_vertical_with_mask(self, cmd: UnifiedCommand) -> bool:
        """Applique FLIP VERTICAL en respectant le masque de sélection"""
        special = cmd.special_selection
        colors = special["params"]
        coords = special["coords"]
        
        if len(coords) != 4:
            return False
            
        x1, y1, x2, y2 = coords
        x1, x2 = min(x1, x2), max(x1, x2)
        y1, y2 = min(y1, y2), max(y1, y2)
        
        if x1 < 0 or x2 >= self.height or y1 < 0 or y2 >= self.width:
            return False
        
        # Créer le masque des cellules sélectionnées dans la région spécifiée
        region = self.grid[x1:x2+1, y1:y2+1]
        mask = np.isin(region, colors)
        
        # Extraire seulement les cellules sélectionnées
        selected_values = region[mask]
        
        if len(selected_values) == 0:
            return True  # Rien à transformer
        
        # Créer une copie de la région pour la transformation
        region_copy = region.copy()
        
        # Appliquer le flip vertical seulement aux cellules sélectionnées
        # 1. Extraire les positions des cellules sélectionnées
        selected_positions = np.where(mask)
        
        # 2. Calculer les nouvelles positions après flip vertical
        height = region.shape[0]
        
        # 3. Sauvegarder les valeurs sélectionnées et leurs nouvelles positions
        old_values = []
        new_positions = []
        
        for old_y, old_x in zip(selected_positions[0], selected_positions[1]):
            new_y = height - 1 - old_y
            new_x = old_x
            if 0 <= new_y < height and 0 <= new_x < region.shape[1]:
                old_values.append(region[old_y, old_x])
                new_positions.append((new_y, new_x))
        
        # 4. Sauvegarder les cellules non-sélectionnées dans la région
        non_selected_backup = {}
        for y in range(region.shape[0]):
            for x in range(region.shape[1]):
                if not mask[y, x]:  # Cellule non-sélectionnée
                    non_selected_backup[(y, x)] = region[y, x]
        
        # 5. Nettoyer seulement les anciennes positions des cellules sélectionnées
        for old_y, old_x in zip(selected_positions[0], selected_positions[1]):
            self.grid[x1 + old_y, y1 + old_x] = 0
        
        # 6. Placer les valeurs aux nouvelles positions
        for value, (new_y, new_x) in zip(old_values, new_positions):
            self.grid[x1 + new_y, y1 + new_x] = value
        
        # 7. Restaurer les cellules non-sélectionnées
        for (y, x), value in non_selected_backup.items():
            self.grid[x1 + y, y1 + x] = value
        
        return True

    def _flip_horizontal_with_mask(self, cmd: UnifiedCommand) -> bool:
        """Applique FLIP HORIZONTAL en respectant le masque de sélection"""
        special = cmd.special_selection
        colors = special["params"]
        coords = special["coords"]
        
        if len(coords) != 4:
            return False
            
        x1, y1, x2, y2 = coords
        x1, x2 = min(x1, x2), max(x1, x2)
        y1, y2 = min(y1, y2), max(y1, y2)
        
        if x1 < 0 or x2 >= self.height or y1 < 0 or y2 >= self.width:
            return False
        
        # Créer le masque des cellules sélectionnées dans la région spécifiée
        region = self.grid[x1:x2+1, y1:y2+1]
        mask = np.isin(region, colors)
        
        # Extraire seulement les cellules sélectionnées
        selected_values = region[mask]
        
        if len(selected_values) == 0:
            return True  # Rien à transformer
        
        # Créer une copie de la région pour la transformation
        region_copy = region.copy()
        
        # Appliquer le flip horizontal seulement aux cellules sélectionnées
        # 1. Extraire les positions des cellules sélectionnées
        selected_positions = np.where(mask)
        
        # 2. Calculer les nouvelles positions après flip horizontal
        width = region.shape[1]
        
        # 3. Sauvegarder les valeurs sélectionnées et leurs nouvelles positions
        old_values = []
        new_positions = []
        
        for old_y, old_x in zip(selected_positions[0], selected_positions[1]):
            new_y = old_y
            new_x = width - 1 - old_x
            if 0 <= new_y < region.shape[0] and 0 <= new_x < width:
                old_values.append(region[old_y, old_x])
                new_positions.append((new_y, new_x))
        
        # 4. Sauvegarder les cellules non-sélectionnées dans la région
        non_selected_backup = {}
        for y in range(region.shape[0]):
            for x in range(region.shape[1]):
                if not mask[y, x]:  # Cellule non-sélectionnée
                    non_selected_backup[(y, x)] = region[y, x]
        
        # 5. Nettoyer seulement les anciennes positions des cellules sélectionnées
        for old_y, old_x in zip(selected_positions[0], selected_positions[1]):
            self.grid[x1 + old_y, y1 + old_x] = 0
        
        # 6. Placer les valeurs aux nouvelles positions
        for value, (new_y, new_x) in zip(old_values, new_positions):
            self.grid[x1 + new_y, y1 + new_x] = value
        
        # 7. Restaurer les cellules non-sélectionnées
        for (y, x), value in non_selected_backup.items():
            self.grid[x1 + y, y1 + x] = value
        
        return True

    def _rotate_left_with_mask(self, cmd: UnifiedCommand) -> bool:
        """Applique ROTATE LEFT en respectant le masque de sélection"""
        special = cmd.special_selection
        colors = special["params"]
        coords = special["coords"]
        
        if len(coords) != 4:
            return False
            
        x1, y1, x2, y2 = coords
        x1, x2 = min(x1, x2), max(x1, x2)
        y1, y2 = min(y1, y2), max(y1, y2)
        
        if x1 < 0 or x2 >= self.height or y1 < 0 or y2 >= self.width:
            return False
        
        # Créer le masque des cellules sélectionnées dans la région spécifiée
        region = self.grid[x1:x2+1, y1:y2+1]
        mask = np.isin(region, colors)
        
        # Extraire seulement les cellules sélectionnées
        selected_values = region[mask]
        
        if len(selected_values) == 0:
            return True  # Rien à transformer
        
        # Créer une copie de la région pour la transformation
        region_copy = region.copy()
        
        # Appliquer la rotation gauche seulement aux cellules sélectionnées
        # 1. Extraire les positions des cellules sélectionnées
        selected_positions = np.where(mask)
        
        # 2. Calculer les nouvelles positions après rotation gauche (90° antihoraire)
        height, width = region.shape
        
        # 3. Sauvegarder les valeurs sélectionnées et leurs nouvelles positions
        old_values = []
        new_positions = []
        
        # Rotation gauche: (y, x) -> (width-1-x, y)
        for old_y, old_x in zip(selected_positions[0], selected_positions[1]):
            new_y = width - 1 - old_x
            new_x = old_y
            # Vérifier que la nouvelle position est dans les limites de la région
            if 0 <= new_y < height and 0 <= new_x < width:
                old_values.append(region[old_y, old_x])
                new_positions.append((new_y, new_x))
        
        # 4. Sauvegarder les cellules non-sélectionnées dans la région
        non_selected_backup = {}
        for y in range(region.shape[0]):
            for x in range(region.shape[1]):
                if not mask[y, x]:  # Cellule non-sélectionnée
                    non_selected_backup[(y, x)] = region[y, x]
        
        # 5. Nettoyer seulement les anciennes positions des cellules sélectionnées
        for old_y, old_x in zip(selected_positions[0], selected_positions[1]):
            self.grid[x1 + old_y, y1 + old_x] = 0
        
        # 6. Placer les valeurs aux nouvelles positions
        for value, (new_y, new_x) in zip(old_values, new_positions):
            self.grid[x1 + new_y, y1 + new_x] = value
        
        # 7. Restaurer les cellules non-sélectionnées
        for (y, x), value in non_selected_backup.items():
            self.grid[x1 + y, y1 + x] = value
        
        return True

    def _cmd_propose(self, cmd: UnifiedCommand) -> bool:
        """Propose la solution actuelle"""
        if self.grid is None:
            return False
        # Cette commande ne fait rien, elle indique simplement que la solution est complète
        return True

    def _cmd_end(self, cmd: UnifiedCommand) -> bool:
        """Termine l'exécution"""
        return True

    def _cmd_select_release(self, cmd: UnifiedCommand) -> bool:
        """Relâche les sélections"""
        return True

    # ==================================================================
    # FONCTIONS GÉNÉRIQUES POUR PARSER LES BLOCS DE COORDONNÉES
    # ==================================================================

    def _parse_coordinate_blocks(self, raw_command: str) -> List[List[str]]:
        """
        Parse les blocs de coordonnées depuis la commande brute
        Retourne un tableau où chaque élément est un bloc de coordonnées :
        - [coord] pour une cellule simple
        - [coord1, coord2] pour un rectangle
        - ['INVERT', coord1, coord2] pour un modificateur INVERT
        - ['COLOR', params, coord1, coord2] pour un modificateur COLOR
        """
        coordinate_blocks = []
        
        if not raw_command:
            return coordinate_blocks
        
        # Vérifier d'abord les nouveaux formats avec modificateurs
        import re
        
        # Détecter (INVERT ([...])) ou (INVERT [...]) ou ancien format SELECT_INVERT([...])
        invert_pattern_double = r'\(INVERT\s*\(([^)]+)\)\)'
        invert_pattern_single = r'\(INVERT\s*\[([^\]]+)\]\)'
        # Support ancien format SELECT_INVERT
        legacy_invert_pattern = r'SELECT_INVERT\s*\(([^)]+)\)'
        
        invert_match = re.search(invert_pattern_double, raw_command)
        invert_content = None
        
        if invert_match:
            invert_content = invert_match.group(1)
        else:
            invert_match = re.search(invert_pattern_single, raw_command)
            if invert_match:
                invert_content = f'[{invert_match.group(1)}]'  # Remettre les crochets pour uniformiser
            else:
                # Vérifier l'ancien format SELECT_INVERT
                invert_match = re.search(legacy_invert_pattern, raw_command)
                if invert_match:
                    invert_content = invert_match.group(1)  # Contenu déjà avec crochets
        
        if invert_content:
            # Extraire TOUTES les coordonnées pour créer UN SEUL bloc INVERT
            coord_pattern = r'\[([^\]]+)\]'
            coord_matches = re.findall(coord_pattern, invert_content)
            all_coords = []
            for coord_match in coord_matches:
                if ' ' in coord_match:
                    coords = [c.strip() for c in coord_match.split() if c.strip()]
                    all_coords.extend(coords)
                else:
                    all_coords.append(coord_match)
            # Créer UN SEUL bloc INVERT avec toutes les coordonnées
            if all_coords:
                coordinate_blocks.append(['INVERT'] + all_coords)
            return coordinate_blocks
        
        # Détecter (COLOR params ([...])) ou (COLOR params [...]) ou ancien format (SELECT_COLOR params [...])
        # Aussi détecter COMMAND (COLOR params [...])
        color_pattern_double = r'\(COLOR\s+([^(]+)\s*\(([^)]+)\)\)'
        color_pattern_single = r'\(COLOR\s+([^\[]+)\s*\[([^\]]+)\]\)'
        color_pattern_with_command = r'\w+\s*\(COLOR\s+([^\[]+)\s*\[([^\]]+)\]\)'
        # Support ancien format SELECT_COLOR
        legacy_color_pattern = r'\(SELECT_COLOR\s+([^\[]+)\s*\[([^\]]+)\]\)'
        
        color_match = re.search(color_pattern_double, raw_command)
        color_params = None
        color_content = None
        
        if color_match:
            color_params = color_match.group(1).strip()
            color_content = color_match.group(2)
        else:
            color_match = re.search(color_pattern_single, raw_command)
            if color_match:
                color_params = color_match.group(1).strip()
                color_content = f'[{color_match.group(2)}]'  # Remettre les crochets pour uniformiser
            else:
                # Nouveau: vérifier COMMAND (COLOR params [...])
                color_match = re.search(color_pattern_with_command, raw_command)
                if color_match:
                    color_params = color_match.group(1).strip()
                    color_content = f'[{color_match.group(2)}]'  # Remettre les crochets pour uniformiser
                else:
                    # Vérifier l'ancien format SELECT_COLOR
                    color_match = re.search(legacy_color_pattern, raw_command)
                    if color_match:
                        color_params = color_match.group(1).strip()
                        color_content = f'[{color_match.group(2)}]'  # Remettre les crochets pour uniformiser
        
        if color_params and color_content:
            # Extraire TOUTES les coordonnées pour créer UN SEUL bloc COLOR
            coord_pattern = r'\[([^\]]+)\]'
            coord_matches = re.findall(coord_pattern, color_content)
            all_coords = []
            for coord_match in coord_matches:
                if ' ' in coord_match:
                    coords = [c.strip() for c in coord_match.split() if c.strip()]
                    all_coords.extend(coords)
                else:
                    all_coords.append(coord_match)
            # Créer UN SEUL bloc COLOR avec toutes les coordonnées
            if all_coords:
                coordinate_blocks.append(['COLOR', color_params] + all_coords)
            return coordinate_blocks
        
        # Format traditionnel : extraire tous les blocs [...]
        block_pattern = r'\[([^\]]+)\]'
        matches = re.finditer(block_pattern, raw_command)
        
        for match in matches:
            block_content = match.group(1).strip()
            
            if ' ' in block_content:
                # Rectangle: "7,6 13,6" -> ["7,6", "13,6"]
                coords = [c.strip() for c in block_content.split() if c.strip()]
                coordinate_blocks.append(coords)
            else:
                # Cellule simple: "17,1" -> ["17,1"]
                coordinate_blocks.append([block_content])
        
        return coordinate_blocks

    def _process_coordinate_blocks(self, coordinate_blocks: List[List[str]], action_func):
        """
        Applique une action sur chaque bloc de coordonnées
        
        Args:
            coordinate_blocks: Blocs de coordonnées parsés
            action_func: Fonction à appliquer pour chaque bloc (x1, y1, x2, y2)
                        Reçoit les coordonnées normalisées (min, min, max, max)
        """
        for block in coordinate_blocks:
            # Détecter les modificateurs de coordonnées
            if len(block) > 0 and block[0] == 'INVERT':
                # Bloc avec modificateur INVERT - traiter toutes les coordonnées comme un ensemble
                coords = block[1:]  # Enlever le premier élément 'INVERT'
                
                # Créer un masque qui exclut toutes les zones spécifiées
                excluded_mask = np.zeros(self.grid.shape, dtype=bool)
                
                # Traiter les coordonnées par paires pour créer des rectangles/cellules
                for i in range(0, len(coords), 2):
                    try:
                        if i + 1 < len(coords):
                            # Rectangle
                            coord1_parts = coords[i].split(',')
                            coord2_parts = coords[i + 1].split(',')
                            if len(coord1_parts) == 2 and len(coord2_parts) == 2:
                                x1, y1 = int(coord1_parts[0]), int(coord1_parts[1])
                                x2, y2 = int(coord2_parts[0]), int(coord2_parts[1])
                                
                                if (0 <= x1 < self.grid.shape[0] and 0 <= x2 < self.grid.shape[0] and
                                    0 <= y1 < self.grid.shape[1] and 0 <= y2 < self.grid.shape[1]):
                                    min_x, max_x = min(x1, x2), max(x1, x2)
                                    min_y, max_y = min(y1, y2), max(y1, y2)
                                    # Marquer cette zone comme exclue
                                    excluded_mask[min_x:max_x+1, min_y:max_y+1] = True
                        else:
                            # Cellule simple
                            coord_parts = coords[i].split(',')
                            if len(coord_parts) == 2:
                                x, y = int(coord_parts[0]), int(coord_parts[1])
                                if (0 <= x < self.grid.shape[0] and 0 <= y < self.grid.shape[1]):
                                    excluded_mask[x, y] = True
                    except (ValueError, IndexError):
                        continue
                
                # Créer le masque final inversé
                final_mask = ~excluded_mask
                
                # Appliquer l'action à toutes les cellules du masque inversé
                original_grid = self.grid.copy()
                for x in range(self.grid.shape[0]):
                    for y in range(self.grid.shape[1]):
                        if final_mask[x, y]:
                            action_func(x, y, x, y)
                        
            elif len(block) > 0 and block[0] == 'COLOR':
                # Bloc avec modificateur COLOR
                if len(block) >= 3:
                    try:
                        color_params = block[1]  # Les paramètres de couleur
                        colors = [int(c.strip()) for c in color_params.split(',')]
                        coords = block[2:]  # Toutes les coordonnées après les paramètres
                        
                        # Traiter les coordonnées par paires pour créer des rectangles/cellules
                        for i in range(0, len(coords), 2):
                            if i + 1 < len(coords):
                                # Rectangle avec COLOR
                                coord1_parts = coords[i].split(',')
                                coord2_parts = coords[i + 1].split(',')
                                if len(coord1_parts) == 2 and len(coord2_parts) == 2:
                                    x1, y1 = int(coord1_parts[0]), int(coord1_parts[1])
                                    x2, y2 = int(coord2_parts[0]), int(coord2_parts[1])
                                    
                                    if (0 <= x1 < self.grid.shape[0] and 0 <= x2 < self.grid.shape[0] and
                                        0 <= y1 < self.grid.shape[1] and 0 <= y2 < self.grid.shape[1]):
                                        min_x, max_x = min(x1, x2), max(x1, x2)
                                        min_y, max_y = min(y1, y2), max(y1, y2)
                                        
                                        # Appliquer l'action seulement aux cellules des couleurs spécifiées
                                        for x in range(min_x, max_x + 1):
                                            for y in range(min_y, max_y + 1):
                                                cell_color = self.grid[x, y]
                                                if cell_color in colors:
                                                    action_func(x, y, x, y)
                            else:
                                # Cellule simple avec COLOR
                                coord_parts = coords[i].split(',')
                                if len(coord_parts) == 2:
                                    x, y = int(coord_parts[0]), int(coord_parts[1])
                                    if (0 <= x < self.grid.shape[0] and 0 <= y < self.grid.shape[1]):
                                        cell_color = self.grid[x, y]
                                        if cell_color in colors:
                                            action_func(x, y, x, y)
                    except (ValueError, IndexError):
                        continue
                        
            elif len(block) == 1:
                # Cellule simple traditionnelle
                try:
                    coord_parts = block[0].split(',')
                    if len(coord_parts) == 2:
                        x, y = int(coord_parts[0]), int(coord_parts[1])
                        if (0 <= x < self.grid.shape[0] and 0 <= y < self.grid.shape[1]):
                            action_func(x, y, x, y)
                except (ValueError, IndexError):
                    continue
                    
            elif len(block) == 2:
                # Rectangle traditionnel
                try:
                    coord1_parts = block[0].split(',')
                    coord2_parts = block[1].split(',')
                    if len(coord1_parts) == 2 and len(coord2_parts) == 2:
                        x1, y1 = int(coord1_parts[0]), int(coord1_parts[1])
                        x2, y2 = int(coord2_parts[0]), int(coord2_parts[1])
                        
                        if (0 <= x1 < self.grid.shape[0] and 0 <= x2 < self.grid.shape[0] and
                            0 <= y1 < self.grid.shape[1] and 0 <= y2 < self.grid.shape[1]):
                            # Normaliser les coordonnées
                            min_x, max_x = min(x1, x2), max(x1, x2)
                            min_y, max_y = min(y1, y2), max(y1, y2)
                            action_func(min_x, min_y, max_x, max_y)
                except (ValueError, IndexError):
                    continue

    # ==================================================================
    # IMPLEMENTATIONS REFACTORISÉES
    # ==================================================================

    def _create_mask_from_special_selection(self, special_selection):
        """
        Crée un masque booléen pour identifier les cellules correspondant à une sélection spéciale.
        
        Args:
            special_selection: dictionnaire contenant le type et les paramètres de la sélection
            
        Returns:
            np.ndarray: masque booléen de la même forme que self.grid, ou None si erreur
        """
        if not special_selection or "type" not in special_selection:
            return None
            
        if special_selection["type"] in ["SELECT_COLOR", "COLOR"]:
            # Extraire les paramètres
            colors = special_selection.get("params", [])
            coords = special_selection.get("coords", [])
            
            if len(coords) != 4 or not colors:
                return None
                
            x1, y1, x2, y2 = coords
            
            # S'assurer que x1 <= x2 et y1 <= y2
            x1, x2 = min(x1, x2), max(x1, x2)
            y1, y2 = min(y1, y2), max(y1, y2)
            
            # Vérifier les limites
            if (x1 < 0 or x2 >= self.grid.shape[0] or 
                y1 < 0 or y2 >= self.grid.shape[1]):
                return None
            
            # Créer un masque global pour toute la grille
            full_mask = np.zeros(self.grid.shape, dtype=bool)
            
            # Créer un masque pour la région spécifiée
            region = self.grid[x1:x2+1, y1:y2+1]
            region_mask = np.isin(region, colors)
            
            # Appliquer le masque de région au masque global
            full_mask[x1:x2+1, y1:y2+1] = region_mask
            
            return full_mask
            
        elif special_selection["type"] == "SELECT_INVERT":
            # Pour SELECT_INVERT, nous inversons une sélection existante
            # Récupérer les coordonnées de la zone à inverser
            coords = special_selection.get("coords", [])
            
            if len(coords) != 4:
                return None
                
            x1, y1, x2, y2 = coords
            x1, x2 = min(x1, x2), max(x1, x2)
            y1, y2 = min(y1, y2), max(y1, y2)
            
            # Vérifier les limites
            if (x1 < 0 or x2 >= self.grid.shape[0] or 
                y1 < 0 or y2 >= self.grid.shape[1]):
                return None
            
            # Créer un masque qui sélectionne PARTOUT SAUF la zone spécifiée
            full_mask = np.ones(self.grid.shape, dtype=bool)
            full_mask[x1:x2+1, y1:y2+1] = False
            
            return full_mask
        
        # Autres types de sélections spéciales peuvent être ajoutés ici
        return None

    def _create_invert_mask(self, x1: int, y1: int, x2: int, y2: int):
        """
        Crée un masque INVERT - sélectionne partout SAUF la zone spécifiée
        """
        mask = np.ones(self.grid.shape, dtype=bool)
        mask[x1:x2+1, y1:y2+1] = False
        return mask

    def _create_color_mask(self, colors: List[int], x1: int, y1: int, x2: int, y2: int):
        """
        Crée un masque COLOR - sélectionne les cellules de couleurs spécifiées dans la zone
        """
        mask = np.zeros(self.grid.shape, dtype=bool)
        region = self.grid[x1:x2+1, y1:y2+1]
        color_mask = np.isin(region, colors)
        mask[x1:x2+1, y1:y2+1] = color_mask
        return mask

    def _apply_action_with_mask(self, action_func, coordinates: List[int], mask):
        """
        Applique une action en respectant le masque fourni
        """
        if len(coordinates) >= 4:
            x1, y1, x2, y2 = coordinates[0], coordinates[1], coordinates[2], coordinates[3]
            
            # Sauvegarder l'état original de la grille
            original_grid = self.grid.copy()
            
            # Appliquer l'action normalement
            action_func(x1, y1, x2, y2)
            
            # Restaurer les cellules non-masquées
            self.grid = np.where(mask, self.grid, original_grid)
