{"original_filename": "1d398264_TEST1_VALID_2025-05-08T17-07-18-408Z.agi", "corrected_filename": "1d398264_TEST1_INVALID_2025-05-08T17-07-18-408Z.agi", "original_filepath": "../arcdata\\evaluation\\1d398264_TEST1_VALID_2025-05-08T17-07-18-408Z.agi", "new_filepath": "../arcdata\\evaluation\\1d398264_TEST1_INVALID_2025-05-08T17-07-18-408Z.agi", "correction_date": "2025-06-15T00:36:17.746943", "validation_errors": ["Commande invalide: EDITS(EDIT 1,6 5; EDIT 0,7 5; EDIT 3,6 5; EDIT 3,7 5; EDIT 3,8 5; EDIT 3,9 5; EDIT 3,10 5; EDIT 3,11 5; EDIT 3,12 5; EDIT 3,13 5; EDIT 3,14 5; EDIT 3,15 5; EDIT 5,6 1; EDIT 6,7 1; EDIT 7,8 1; EDIT 8,9 1; EDIT 9,10 1; EDIT 10,11 1; EDIT 11,12 1; EDIT 12,13 1; EDIT 13,14 1; EDIT 14,15 1; EDIT 5,4 1; EDIT 6,4 1; EDIT 7,4 1; EDIT 8,4 1; EDIT 9,4 1; EDIT 10,4 1; EDIT 11,4 1; EDIT 12,4 1; EDIT 13,4 1; EDIT 14,4 1; EDIT 15,4 1; EDIT 5,2 4; EDIT 6,1 4; EDIT 7,0 4; EDIT 1,2 4; EDIT 0,1 4; EDIT 3,2 2; EDIT 3,1 2; EDIT 3,0 2; EDIT 1,4 2; EDIT 0,4 2)", "Commande invalide: 47"], "task_id": "1d398264", "test_index": 1}