#!/usr/bin/env python3
"""
Script de validation simple utilisant le vrai backend
Ce script reproduit exactement le processus de validation utilisé par le frontend.

Utilisation:
    python validation_backend_simple.py [options]
    
Options:
    --task TASK_ID   : Teste uniquement une tâche spécifique (ex: 007bbfb7)
    --max N          : Limite le nombre de scénarios à tester
    --output FILE    : Fichier de sortie pour le rapport JSON
    --markdown FILE  : Fichier de sortie pour le rapport Markdown détaillé (plan d'action)
    --subnet SUBSET  : Sous-répertoire à utiliser (ex: training, evaluation) - défaut: training
    --verbose        : Affichage détaillé
"""

import sys
import os
import django
from pathlib import Path
import json
import glob
from datetime import datetime
import argparse
from collections import defaultdict, Counter

# Configuration Django
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), 'backend'))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

# Import du service de validation backend (le même que le frontend utilise)
from myapp.services.scenario_backend_validation_service import ScenarioBackendValidationService

def get_all_training_scenarios(max_scenarios=None, specific_task=None, subset='training'):
    """Récupère tous les fichiers .agi du répertoire spécifié."""
    training_dir = Path(f'arcdata/{subset}')
    
    if not training_dir.exists():
        print(f"❌ Répertoire {training_dir} non trouvé")
        return []
    
    # Pattern pour les fichiers .agi
    if specific_task:
        pattern = f"{specific_task}_*.agi"
    else:
        pattern = "*.agi"
    
    agi_files = list(training_dir.glob(pattern))
    
    if max_scenarios:
        agi_files = agi_files[:max_scenarios]
    
    scenarios = []
    for agi_file in agi_files:
        # Extraire task_id et test_index du nom de fichier
        # Format: TASK_ID_TEST{index}_VALID.agi
        filename = agi_file.stem
        parts = filename.split('_')
        if len(parts) >= 3:
            task_id = parts[0]
            test_part = parts[1]
            if test_part.startswith('TEST'):
                try:
                    test_index = int(test_part[4:])  # Extraire le numéro après 'TEST'
                    scenarios.append({
                        'file_path': str(agi_file),
                        'task_id': task_id,
                        'test_index': test_index,
                        'filename': filename
                    })
                except ValueError:
                    print(f"⚠️ Impossible d'extraire l'index de test de {filename}")
    
    return scenarios

def generate_markdown_report(results, total_scenarios, successful_validations, failed_validations, success_rate, output_file, subset='training'):
    """Génère un rapport Markdown détaillé pour identifier les scénarios à corriger."""
    
    # Analyser les types d'erreurs
    error_types = defaultdict(list)
    failed_scenarios = [r for r in results if r['status'] == 'FAILED']
    
    for result in failed_scenarios:
        error_msg = result.get('error_message', 'Erreur inconnue')
        if 'Erreur d\'exécution des commandes' in error_msg:
            error_types['Erreurs d\'exécution'].append(result)
        elif 'ne correspond pas' in error_msg or 'matching_cells' in str(result.get('validation_details', {})):
            error_types['Grilles non correspondantes'].append(result)
        elif 'timeout' in error_msg.lower():
            error_types['Timeout'].append(result)
        elif 'parsing' in error_msg.lower() or 'parse' in error_msg.lower():
            error_types['Erreurs de parsing'].append(result)
        else:
            error_types['Autres erreurs'].append(result)
    
    # Analyser les tâches les plus problématiques
    task_failures = defaultdict(int)
    task_successes = defaultdict(int)
    all_tasks = set()
    
    for result in results:
        all_tasks.add(result['task_id'])
        if result['status'] == 'FAILED':
            task_failures[result['task_id']] += 1
        else:
            task_successes[result['task_id']] += 1
    
    # Identifier les tâches sans scénarios (présentes dans le sous-répertoire spécifié mais pas testées)
    subset_dir = Path(f'arcdata/{subset}')
    all_json_tasks = set()
    if subset_dir.exists():
        json_files = list(subset_dir.glob('*.json'))
        for json_file in json_files:
            if json_file.stem not in ['metadata', 'backups']:
                all_json_tasks.add(json_file.stem)
    
    tasks_without_scenarios = all_json_tasks - all_tasks
    
    # Classer les tâches par priorité
    most_problematic_tasks = sorted(task_failures.items(), key=lambda x: x[1], reverse=True)[:10]
    
    # Classer les erreurs par fréquence
    error_types_sorted = sorted(error_types.items(), key=lambda x: len(x[1]), reverse=True)
    
    # Générer le rapport Markdown
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(f"# Rapport de Validation Backend - Plan d'Action pour 100%\n\n")
        f.write(f"**Date:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        # Résumé exécutif
        f.write("## 📊 Résumé Exécutif\n\n")
        f.write(f"- **Total des scénarios:** {total_scenarios}\n")
        f.write(f"- **Succès:** {successful_validations} ({success_rate:.1f}%)\n")
        f.write(f"- **Échecs:** {failed_validations} ({100-success_rate:.1f}%)\n")
        f.write(f"- **Objectif:** Corriger {failed_validations} scénarios pour atteindre 100%\n\n")
        
        # Priorités d'action
        f.write("## 🎯 Priorités d'Action\n\n")
        
        if success_rate >= 90:
            f.write("**🟢 PRIORITÉ HAUTE:** Vous êtes très proche de 100% ! Focus sur les derniers bugs.\n\n")
        elif success_rate >= 70:
            f.write("**🟡 PRIORITÉ MOYENNE:** Bon taux de succès, quelques corrections ciblées nécessaires.\n\n")
        else:
            f.write("**🔴 PRIORITÉ CRITIQUE:** Taux de succès faible, corrections majeures requises.\n\n")
        
        # Analyse des types d'erreurs (classés par fréquence)
        f.write("## 🔍 Analyse des Types d'Erreurs (Classés par Fréquence)\n\n")
        
        for error_type, scenarios in error_types_sorted:
            percentage = (len(scenarios) / failed_validations * 100) if failed_validations > 0 else 0
            f.write(f"### {error_type} ({len(scenarios)} scénarios - {percentage:.1f}% des échecs)\n\n")
            
            if error_type == 'Erreurs d\'exécution':
                f.write("**🔧 Actions recommandées:**\n")
                f.write("- Vérifier la syntaxe des commandes dans les fichiers .agi\n")
                f.write("- Corriger les erreurs de parsing des commandes\n")
                f.write("- Valider les paramètres des commandes\n\n")
            elif error_type == 'Grilles non correspondantes':
                f.write("**🎯 Actions recommandées:**\n")
                f.write("- Analyser les algorithmes de transformation\n")
                f.write("- Corriger la logique métier des commandes\n")
                f.write("- Vérifier les calculs de coordonnées\n\n")
            elif error_type == 'Timeout':
                f.write("**⚡ Actions recommandées:**\n")
                f.write("- Optimiser les algorithmes lents\n")
                f.write("- Réduire la complexité des opérations\n")
                f.write("- Implémenter des optimisations de performance\n\n")
            elif error_type == 'Erreurs de parsing':
                f.write("**📝 Actions recommandées:**\n")
                f.write("- Corriger le parser de commandes\n")
                f.write("- Valider le format des fichiers .agi\n")
                f.write("- Améliorer la gestion des erreurs de syntaxe\n\n")
            
            # Lister les premiers scénarios de ce type avec détails d'erreur
            f.write("**Scénarios concernés:**\n")
            for i, scenario in enumerate(scenarios[:5]):
                # Vérifier que scenario n'est pas None et est un dictionnaire
                if scenario is None or not isinstance(scenario, dict):
                    f.write(f"- Scénario invalide (None ou non-dictionnaire)\n")
                    continue
                    
                f.write(f"- `{scenario.get('filename', 'N/A')}` (Tâche: {scenario.get('task_id', 'N/A')})\n")
                
                # Ajouter les détails d'erreur si disponibles
                validation_details = scenario.get('validation_details') if scenario else None
                execution_result = validation_details.get('execution_result') if validation_details else None
                command_errors = execution_result.get('command_errors') if execution_result else None
                
                if command_errors:
                    cmd_errors = command_errors
                    for cmd_error in cmd_errors[:3]:  # Limiter à 3 erreurs par scénario
                        if isinstance(cmd_error, dict):
                            line_num = cmd_error.get('line', 'N/A')
                            cmd_name = cmd_error.get('command', 'N/A')
                            error_type = cmd_error.get('type', 'unknown')
                            error_message = cmd_error.get('message', 'N/A')
                            suggestions = cmd_error.get('suggestions', [])
                            
                            f.write(f"  - Ligne {line_num}: `{cmd_name}` - {error_type}: {error_message}\n")
                            if suggestions:
                                f.write(f"    Suggestions: {', '.join([f'`{s}`' for s in suggestions])}\n")
                        else:
                            f.write(f"  - {cmd_error}\n")
                    if len(cmd_errors) > 3:
                        f.write(f"  - ... et {len(cmd_errors) - 3} autres erreurs\n")
                        
            if len(scenarios) > 5:
                f.write(f"- ... et {len(scenarios) - 5} autres\n")
            f.write("\n")
        
        # Tâches sans scénarios
        if tasks_without_scenarios:
            f.write("## ⚠️ Tâches Sans Scénarios de Test\n\n")
            f.write(f"**{len(tasks_without_scenarios)} tâches** présentes dans arcdata mais sans scénarios .agi:\n\n")
            
            tasks_without_scenarios_sorted = sorted(list(tasks_without_scenarios))
            for i, task_id in enumerate(tasks_without_scenarios_sorted[:20], 1):
                f.write(f"{i}. `{task_id}`\n")
            
            if len(tasks_without_scenarios) > 20:
                f.write(f"... et {len(tasks_without_scenarios) - 20} autres\n")
            
            f.write("\n**🔧 Actions recommandées:**\n")
            f.write("- Créer des scénarios .agi pour ces tâches\n")
            f.write("- Vérifier si ces tâches sont pertinentes pour la validation\n")
            f.write("- Prioriser les tâches les plus importantes\n\n")
        
        # Analyse des commandes inconnues
        unknown_commands = defaultdict(list)
        command_suggestions = defaultdict(set)
        
        for result in failed_scenarios:
            validation_details = result.get('validation_details') if result and isinstance(result, dict) else None
            execution_result = validation_details.get('execution_result') if validation_details else None
            command_errors = execution_result.get('command_errors') if execution_result else None
            
            if command_errors:
                cmd_errors = command_errors
                for cmd_error in cmd_errors:
                    if isinstance(cmd_error, dict) and cmd_error.get('type') == 'execution' and 'Commande inconnue' in cmd_error.get('message', ''):
                        unknown_cmd = cmd_error.get('command', 'N/A')
                        unknown_commands[unknown_cmd].append(result.get('filename', 'N/A'))
                        suggestions = cmd_error.get('suggestions', [])
                        for suggestion in suggestions:
                            command_suggestions[unknown_cmd].add(suggestion)
        
        if unknown_commands:
            f.write("## ⚠️ Commandes Inconnues Détectées\n\n")
            f.write("Ces commandes ne sont pas reconnues par le système et nécessitent une attention particulière:\n\n")
            
            f.write("| Commande Inconnue | Occurrences | Suggestions | Scénarios Affectés |\n")
            f.write("|-------------------|-------------|-------------|-------------------|\n")
            
            for unknown_cmd, scenarios in sorted(unknown_commands.items(), key=lambda x: len(x[1]), reverse=True):
                suggestions_str = ", ".join([f"`{s}`" for s in sorted(command_suggestions[unknown_cmd])]) if command_suggestions[unknown_cmd] else "Aucune"
                scenarios_str = ", ".join([f"`{s}`" for s in scenarios[:3]])
                if len(scenarios) > 3:
                    scenarios_str += f" (+{len(scenarios)-3})"
                
                f.write(f"| `{unknown_cmd}` | {len(scenarios)} | {suggestions_str} | {scenarios_str} |\n")
            
            f.write("\n**🔧 Actions recommandées pour les commandes inconnues:**\n")
            f.write("- Vérifier l'orthographe des commandes\n")
            f.write("- Consulter la documentation des commandes supportées\n")
            f.write("- Utiliser les suggestions proposées si disponibles\n")
            f.write("- Mettre à jour les fichiers .agi avec les bonnes commandes\n\n")
        
        # Tâches les plus problématiques
        f.write("## 🚨 Tâches les Plus Problématiques (Classées par Échecs)\n\n")
        f.write("Ces tâches ont le plus d'échecs et devraient être prioritaires:\n\n")
        
        f.write("| Rang | Tâche | Échecs | Succès | Taux d'Échec |\n")
        f.write("|------|-------|--------|--------|--------------|\n")
        
        for i, (task_id, failure_count) in enumerate(most_problematic_tasks[:15], 1):
            success_count = task_successes.get(task_id, 0)
            total_tests = failure_count + success_count
            failure_rate = (failure_count / total_tests * 100) if total_tests > 0 else 0
            f.write(f"| {i} | `{task_id}` | {failure_count} | {success_count} | {failure_rate:.1f}% |\n")
        f.write("\n")
        
        # Statistiques globales
        f.write("## 📊 Statistiques Globales\n\n")
        f.write(f"- **Tâches testées:** {len(all_tasks)}\n")
        f.write(f"- **Tâches avec échecs:** {len(task_failures)}\n")
        f.write(f"- **Tâches sans scénarios:** {len(tasks_without_scenarios)}\n")
        f.write(f"- **Total tâches dans arcdata:** {len(all_json_tasks)}\n")
        f.write(f"- **Couverture de test:** {len(all_tasks) / len(all_json_tasks) * 100 if all_json_tasks else 0:.1f}%\n\n")
        
        # Liste détaillée des échecs (classée par fréquence)
        f.write("## 📋 Liste Détaillée des Échecs (Classée par Fréquence)\n\n")
        
        for error_type, scenarios in error_types_sorted:
            f.write(f"### {error_type}\n\n")
            f.write("| Scénario | Tâche | Erreur | Commandes | Détails |\n")
            f.write("|----------|-------|--------|-----------|---------|\n")
            
            for scenario in scenarios:
                # Vérifier que scenario n'est pas None et est un dictionnaire
                if scenario is None or not isinstance(scenario, dict):
                    continue
                    
                error_short = scenario.get('error_message', 'N/A')[:50] + '...' if len(scenario.get('error_message', '')) > 50 else scenario.get('error_message', 'N/A')
                
                # Extraire les détails d'erreur spécifiques
                details = ""
                validation_details = scenario.get('validation_details') if isinstance(scenario, dict) else None
                execution_result = validation_details.get('execution_result') if validation_details else None
                command_errors = execution_result.get('command_errors') if execution_result else None
                
                if command_errors:
                    cmd_errors = command_errors
                    error_details = []
                    for cmd_error in cmd_errors[:2]:  # Limiter à 2 erreurs pour le tableau
                        if isinstance(cmd_error, dict):
                            cmd_name = cmd_error.get('command', 'N/A')
                            error_type_detail = cmd_error.get('type', 'unknown')
                            error_details.append(f"{cmd_name}({error_type_detail})")
                        else:
                            error_details.append(str(cmd_error)[:20])
                    if error_details:
                        details = "; ".join(error_details)
                        if len(cmd_errors) > 2:
                            details += f"; +{len(cmd_errors)-2}"
                
                f.write(f"| `{scenario.get('filename', 'N/A')}` | {scenario.get('task_id', 'N/A')} | {error_short} | {scenario.get('command_count', 0)} | {details} |\n")
            f.write("\n")
        
        # Plan d'action étape par étape
        f.write("## 📝 Plan d'Action Étape par Étape\n\n")
        f.write("### Phase 1: Corrections Critiques\n")
        f.write("1. **Corriger les erreurs d'exécution** (impact immédiat)\n")
        f.write("2. **Fixer les erreurs de parsing** (stabilité)\n")
        f.write("3. **Résoudre les timeouts** (performance)\n\n")
        
        f.write("### Phase 2: Améliorations Algorithmiques\n")
        f.write("1. **Analyser les grilles non correspondantes**\n")
        f.write("2. **Corriger la logique métier**\n")
        f.write("3. **Optimiser les transformations**\n\n")
        
        f.write("### Phase 3: Validation et Tests\n")
        f.write("1. **Tester les corrections sur les tâches problématiques**\n")
        f.write("2. **Valider l'ensemble des scénarios**\n")
        f.write("3. **Atteindre 100% de succès**\n\n")
        
        # Commandes utiles
        f.write("## 🛠️ Commandes Utiles\n\n")
        f.write("```bash\n")
        f.write("# Tester un scénario spécifique avec détails d'erreur\n")
        f.write("python validation_backend_simple.py --task TASK_ID --verbose\n\n")
        f.write("# Générer un nouveau rapport avec analyse des commandes inconnues\n")
        f.write("python validation_backend_simple.py --markdown rapport_action.md\n\n")
        f.write("# Tester les corrections avec affichage détaillé\n")
        f.write("python validation_backend_simple.py --max 50 --verbose\n\n")
        f.write("# Analyser spécifiquement les erreurs de commandes\n")
        f.write("python validation_backend_simple.py --max 10 --verbose --markdown erreurs_detaillees.md\n")
        f.write("```\n\n")
        f.write("**💡 Nouvelles fonctionnalités:**\n")
        f.write("- Affichage détaillé des commandes inconnues avec suggestions\n")
        f.write("- Analyse des types d'erreur par ligne de commande\n")
        f.write("- Rapport Markdown enrichi avec détails techniques\n")
        f.write("- Identification automatique des corrections prioritaires\n\n")
        
        f.write("---\n")
        f.write(f"*Rapport généré le {datetime.now().strftime('%Y-%m-%d à %H:%M:%S')}*\n")

def validate_scenario_with_backend(validation_service, scenario_info, subset='training', verbose=False):
    """Valide un scénario en utilisant le service backend."""
    try:
        # Vérifier que scenario_info est un dictionnaire
        if not isinstance(scenario_info, dict):
            return False, {"error_message": "Le scénario n'est pas un dictionnaire valide"}
            
        # Lire le contenu du fichier .agi
        with open(scenario_info.get('file_path', ''), 'r', encoding='utf-8') as f:
            content = f.read().strip()
        
        if verbose:
            print(f"📄 Contenu du scénario ({len(content)} caractères):\n")
            print(f"   {content[:100]}{'...' if len(content) > 100 else ''}")
        
        # Utiliser le service de validation backend (même que le frontend)
        result = validation_service.validate_scenario(
            subset=subset,
            task_id=scenario_info.get('task_id', 'N/A'),
            content=content,
            test_index=scenario_info.get('test_index', 0)
        )
        
        return True, result
        
    except Exception as e:
        error_msg = f"Erreur lors de la validation: {str(e)}"
        return False, {'error_message': error_msg, 'is_valid_by_backend': False}

def main():
    parser = argparse.ArgumentParser(description='Validation simple avec le vrai backend')
    parser.add_argument('--task', help='Tester uniquement une tâche spécifique')
    parser.add_argument('--max', type=int, help='Nombre maximum de scénarios à tester')
    parser.add_argument('--output', help='Fichier de sortie pour le rapport JSON')
    parser.add_argument('--markdown', help='Fichier de sortie pour le rapport Markdown détaillé')
    parser.add_argument('--subnet', default='training', help='Sous-répertoire à utiliser (ex: training, evaluation) - défaut: training')
    parser.add_argument('--verbose', action='store_true', help='Affichage détaillé')
    
    args = parser.parse_args()
    
    print("🚀 Démarrage de la validation avec le vrai backend...")
    print(f"📁 Répertoire: arcdata/{args.subnet}")
    
    # Récupérer les scénarios
    scenarios = get_all_training_scenarios(args.max, args.task, args.subnet)
    
    if not scenarios:
        print("❌ Aucun scénario trouvé")
        return
    
    print(f"📊 {len(scenarios)} scénario(s) à valider")
    
    # Initialiser le service de validation backend
    validation_service = ScenarioBackendValidationService(no_grids=not args.verbose)
    
    # Statistiques
    total_scenarios = len(scenarios)
    successful_validations = 0
    failed_validations = 0
    results = []
    
    print("\n" + "="*60)
    print("DÉBUT DE LA VALIDATION")
    print("="*60)
    
    for i, scenario_info in enumerate(scenarios, 1):
        # Vérifier que scenario_info n'est pas None et est un dictionnaire
        if scenario_info is None or not isinstance(scenario_info, dict):
            print(f"\n[{i}/{total_scenarios}] ⚠️ Scénario invalide (None ou non-dictionnaire)")
            continue
            
        success, result = validate_scenario_with_backend(validation_service, scenario_info, args.subnet, args.verbose)
        
        if success == False:
            print(f"\n[{i}/{total_scenarios}] 🔍 {scenario_info.get('filename', 'N/A')}")
        
        if success and result.get('is_valid_by_backend', False):
            #print(f"   ✅ VALIDE - {result.get('command_count', 0)} commandes")
            successful_validations += 1
            status = "SUCCESS"
        else:
            error_msg = result.get('error_message', 'Erreur inconnue')
            execution_result = result.get('execution_result')
            print(f"   ❌ ÉCHEC - result: {execution_result.get('error', 'task')}")
            #print(f"   ❌ ÉCHEC - {error_msg} result: {result}, result error: {result.get('error')}, execution_result: {result.get('execution_result') }")
            
            # Afficher des détails sur l'erreur d'exécution même sans mode verbose
            if "Erreur d'exécution des commandes" in error_msg and result.get('execution_result') and not args.verbose:
                exec_result = result.get('execution_result', {})
                command_errors = exec_result.get('command_errors', [])
                if command_errors:
                    print(f"   🔍 Détails des erreurs de commandes :")
                    for cmd_error in command_errors[:13]:  # Limiter à 3 pour la clarté
                        if isinstance(cmd_error, dict):
                            line = cmd_error.get('line', 'N/A')
                            command = cmd_error.get('command', 'N/A')
                            message = cmd_error.get('message', 'Aucun message')
                            print(f"     - Ligne {line} (`{command}`): {message}")
                        else:
                            print(f"     - {cmd_error}")
                    if len(command_errors) > 3:
                        print(f"     - ... et {len(command_errors) - 3} autres erreurs.")

            # Afficher les détails d'erreur si disponibles et en mode verbose
            if args.verbose and result.get('execution_result'):
                exec_result = result.get('execution_result', {})
                if exec_result.get('command_errors'):
                    print(f"   🔍 Erreurs de commandes détectées:")
                    for cmd_error in exec_result.get('command_errors', []):
                        if isinstance(cmd_error, dict):
                            line_num = cmd_error.get('line', 'N/A')
                            cmd_name = cmd_error.get('command', 'N/A')
                            error_type = cmd_error.get('type', 'unknown')
                            error_message = cmd_error.get('message', 'N/A')
                            suggestions = cmd_error.get('suggestions', [])
                            
                            print(f"      Ligne {line_num}: {cmd_name}")
                            print(f"      Type: {error_type}")
                            print(f"      Message: {error_message}")
                            if suggestions:
                                print(f"      Suggestions: {', '.join(suggestions)}")
                        else:
                            print(f"      {cmd_error}")
            
            failed_validations += 1
            status = "FAILED"
        
        # Collecter les résultats
        result_entry = {
            'filename': scenario_info.get('filename', 'N/A'),
            'task_id': scenario_info.get('task_id', 'N/A'),
            'test_index': scenario_info.get('test_index', 0),
            'status': status,
            'command_count': result.get('command_count', 0),
            'error_message': result.get('error_message'),
            'validation_details': result
        }
        results.append(result_entry)
        
        if args.verbose and result.get('comparison_details'):
            comp = result.get('comparison_details', {})
            print(f"   📊 Comparaison: {comp.get('matching_cells', 0)}/{comp.get('total_cells_expected', 0)} cellules")
    
    # Afficher le bilan final
    print("\n" + "="*60)
    print("BILAN FINAL")
    print("="*60)
    
    success_rate = (successful_validations / total_scenarios * 100) if total_scenarios > 0 else 0
    
    print(f"📊 Total des scénarios testés: {total_scenarios}")
    print(f"✅ Validations réussies: {successful_validations}")
    print(f"❌ Validations échouées: {failed_validations}")
    print(f"📈 Taux de succès: {success_rate:.1f}%")
    
    if success_rate >= 80:
        print("🎉 Excellent taux de succès!")
    elif success_rate >= 50:
        print("⚠️ Taux de succès moyen")
    else:
        print("🚨 Faible taux de succès")
    
    # Sauvegarder le rapport si demandé
    if args.output:
        report = {
            'timestamp': datetime.now().isoformat(),
            'total_scenarios': total_scenarios,
            'successful_validations': successful_validations,
            'failed_validations': failed_validations,
            'success_rate': success_rate,
            'results': results
        }
        
        with open(args.output, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 Rapport sauvegardé dans: {args.output}")
    
    # Générer le rapport Markdown si demandé
    if args.markdown:
        generate_markdown_report(results, total_scenarios, successful_validations, failed_validations, success_rate, args.markdown, args.subnet)
        print(f"\n📝 Rapport Markdown sauvegardé dans: {args.markdown}")
    
    print(f"\n⏱️ Validation terminée")

if __name__ == "__main__":
    main()