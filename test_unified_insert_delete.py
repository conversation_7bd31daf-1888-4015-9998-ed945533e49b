#!/usr/bin/env python3
"""
Test des commandes INSERT et DELETE avec le système de commandes unifiées
"""

import sys
import os

# Ajouter le chemin du backend
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

try:
    from command_system.command_executor import CommandExecutor
    from command_system.unified_command import UnifiedCommand
except ImportError as e:
    print(f"Erreur d'import: {e}")
    sys.exit(1)

def print_grid(grid, title="Grille"):
    """Affiche une grille de manière lisible"""
    print(f"\n{title}:")
    if grid is None:
        print("  None")
        return
    
    for row in grid:
        print("  " + " ".join(str(cell) for cell in row))

def test_unified_parsing():
    """Test du parsing des commandes unifiées INSERT et DELETE"""
    print("=== TEST PARSING COMMANDES UNIFIÉES ===")
    
    test_commands = [
        "INSERT 2 ROWS ABOVE",
        "INSERT 1 COLUMNS AFTER",
        "INSERT 3 ROWS BELOW [1,1]",
        "INSERT 2 COLUMNS BEFORE [0,0 2,2]",
        "DELETE ROWS [1,0 1,3]",
        "DELETE COLUMNS [0,1 2,1]",
    ]
    
    for cmd_str in test_commands:
        try:
            parsed = UnifiedCommand.parse(cmd_str)
            if parsed:
                print(f"✅ {cmd_str}")
                print(f"   Action: {parsed.action}")
                print(f"   Parameters: {parsed.parameters}")
                print(f"   Coordinates: {parsed.coordinates}")
            else:
                print(f"❌ {cmd_str} - Parsing failed")
        except Exception as e:
            print(f"❌ {cmd_str} - Exception: {e}")

def test_real_scenarios():
    """Test de scénarios réels avec les commandes unifiées"""
    print("\n\n=== TEST SCÉNARIOS RÉELS ===")
    
    executor = CommandExecutor()
    
    # Scénario 1: Créer un motif en croix et le modifier
    print("\n1. Création d'un motif en croix")
    commands = [
        "INIT 5x5",
        "EDIT 1 [2,0]", "EDIT 1 [2,1]", "EDIT 1 [2,2]", "EDIT 1 [2,3]", "EDIT 1 [2,4]",  # Ligne horizontale
        "EDIT 1 [0,2]", "EDIT 1 [1,2]", "EDIT 1 [3,2]", "EDIT 1 [4,2]",  # Ligne verticale
    ]
    
    result = executor.execute_commands(commands)
    print(f"Succès: {result['success']}")
    if result['error']:
        print(f"Erreur: {result['error']}")
    print_grid(result['grid'], "Motif en croix initial")
    
    # Ajouter une ligne au milieu pour épaissir la croix
    commands.append("INSERT 1 ROWS BELOW [2,2]")
    result = executor.execute_commands(commands)
    print(f"Succès: {result['success']}")
    if result['error']:
        print(f"Erreur: {result['error']}")
    print_grid(result['grid'], "Après insertion ligne")
    
    # Remplir la nouvelle ligne pour compléter la croix
    commands.extend([
        "EDIT 1 [3,0]", "EDIT 1 [3,1]", "EDIT 1 [3,2]", "EDIT 1 [3,3]", "EDIT 1 [3,4]"
    ])
    result = executor.execute_commands(commands)
    print(f"Succès: {result['success']}")
    if result['error']:
        print(f"Erreur: {result['error']}")
    print_grid(result['grid'], "Croix épaissie")
    
    # Scénario 2: Créer un cadre et le redimensionner
    print("\n\n2. Création et redimensionnement d'un cadre")
    frame_commands = [
        "INIT 4x4",
        # Créer un cadre
        "EDIT 2 [0,0]", "EDIT 2 [0,1]", "EDIT 2 [0,2]", "EDIT 2 [0,3]",  # Haut
        "EDIT 2 [3,0]", "EDIT 2 [3,1]", "EDIT 2 [3,2]", "EDIT 2 [3,3]",  # Bas
        "EDIT 2 [1,0]", "EDIT 2 [2,0]",  # Gauche
        "EDIT 2 [1,3]", "EDIT 2 [2,3]",  # Droite
    ]
    
    result = executor.execute_commands(frame_commands)
    print(f"Succès: {result['success']}")
    print_grid(result['grid'], "Cadre initial 4x4")
    
    # Élargir le cadre en ajoutant des colonnes
    frame_commands.extend([
        "INSERT 1 COLUMNS BEFORE",  # Ajouter colonne à gauche
        "INSERT 1 COLUMNS AFTER",   # Ajouter colonne à droite
    ])
    result = executor.execute_commands(frame_commands)
    print(f"Succès: {result['success']}")
    print_grid(result['grid'], "Cadre élargi")
    
    # Compléter le nouveau cadre
    frame_commands.extend([
        "EDIT 2 [0,0]", "EDIT 2 [0,5]",  # Nouveaux coins haut
        "EDIT 2 [3,0]", "EDIT 2 [3,5]",  # Nouveaux coins bas
        "EDIT 2 [1,0]", "EDIT 2 [2,0]",  # Nouvelle bordure gauche
        "EDIT 2 [1,5]", "EDIT 2 [2,5]",  # Nouvelle bordure droite
    ])
    result = executor.execute_commands(frame_commands)
    print(f"Succès: {result['success']}")
    print_grid(result['grid'], "Cadre complété")

def test_error_handling():
    """Test de la gestion d'erreurs"""
    print("\n\n=== TEST GESTION D'ERREURS ===")
    
    executor = CommandExecutor()
    
    error_cases = [
        ("INSERT sans nombre", "INSERT ROWS ABOVE"),
        ("INSERT nombre négatif", "INSERT -1 ROWS ABOVE"),
        ("INSERT type invalide", "INSERT 1 INVALID ABOVE"),
        ("INSERT position invalide", "INSERT 1 ROWS INVALID"),
        ("DELETE sans type", "DELETE [0,0]"),
        ("DELETE type invalide", "DELETE INVALID [0,0]"),
        ("DELETE sans coordonnées", "DELETE ROWS"),
        ("DELETE coordonnées invalides", "DELETE ROWS [10,0 10,2]"),
    ]
    
    for description, command in error_cases:
        result = executor.execute_commands(["INIT 3x3", command])
        print(f"{description}: {'❌' if not result['success'] else '⚠️'}")
        if result['error']:
            print(f"  Erreur: {result['error']}")

if __name__ == "__main__":
    test_unified_parsing()
    test_real_scenarios()
    test_error_handling()
    print("\n=== TOUS LES TESTS TERMINÉS ===")
