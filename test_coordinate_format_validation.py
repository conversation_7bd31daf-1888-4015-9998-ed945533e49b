#!/usr/bin/env python3
"""
Test de validation du format des coordonnées selon FONCTIONNEMENT_GENERAL_COMMANDES_UNIFIEES.md
"""

import sys
import os

# Ajouter le chemin du backend
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

try:
    from command_system.unified_command import UnifiedCommand
except ImportError as e:
    print(f"Erreur d'import: {e}")
    sys.exit(1)

def test_coordinate_semantics():
    """Test de la sémantique correcte des coordonnées"""
    print("=== TEST SÉMANTIQUE DES COORDONNÉES ===")
    print("Format officiel selon FONCTIONNEMENT_GENERAL_COMMANDES_UNIFIEES.md :")
    print("- Cellule individuelle : [ligne,colonne]")
    print("- Zone rectangulaire : [ligne1,colonne1 ligne2,colonne2]")
    print()
    
    test_cases = [
        {
            "command": "EDIT 7 [2,3]",
            "description": "Cellule individuelle (ligne 2, colonne 3)",
            "expected_coords": [2, 3],
            "semantic": "Éditer la cellule à la ligne 2, colonne 3"
        },
        {
            "command": "FILL 5 [5,6 10,9]", 
            "description": "Zone rectangulaire (de ligne 5,colonne 6 à ligne 10,colonne 9)",
            "expected_coords": [5, 6, 10, 9],
            "semantic": "Remplir la zone rectangulaire de (5,6) à (10,9)"
        },
        {
            "command": "CLEAR [0,0 3,3]",
            "description": "Zone rectangulaire (de ligne 0,colonne 0 à ligne 3,colonne 3)",
            "expected_coords": [0, 0, 3, 3],
            "semantic": "Effacer la zone rectangulaire de (0,0) à (3,3)"
        },
        {
            "command": "COPY [1,1 4,4]",
            "description": "Zone rectangulaire pour copie",
            "expected_coords": [1, 1, 4, 4],
            "semantic": "Copier la zone rectangulaire de (1,1) à (4,4)"
        }
    ]
    
    for test_case in test_cases:
        print(f"Test: {test_case['command']}")
        print(f"Description: {test_case['description']}")
        print(f"Sémantique: {test_case['semantic']}")
        
        try:
            parsed = UnifiedCommand.parse(test_case['command'])
            if parsed:
                print(f"  Coordonnées parsées: {parsed.coordinates}")
                print(f"  Coordonnées attendues: {test_case['expected_coords']}")
                
                if parsed.coordinates == test_case['expected_coords']:
                    print(f"  ✅ Format correct selon la documentation")
                else:
                    print(f"  ❌ Format incorrect")
                    
                # Vérification sémantique
                if len(parsed.coordinates) == 2:
                    ligne, colonne = parsed.coordinates
                    print(f"  📍 Cellule: ligne {ligne}, colonne {colonne}")
                elif len(parsed.coordinates) == 4:
                    ligne1, colonne1, ligne2, colonne2 = parsed.coordinates
                    print(f"  📐 Rectangle: de (ligne {ligne1}, colonne {colonne1}) à (ligne {ligne2}, colonne {colonne2})")
                    
            else:
                print(f"  ❌ Parsing failed")
        except Exception as e:
            print(f"  ❌ Exception: {e}")
        print()

def test_coordinate_edge_cases():
    """Test des cas limites pour les coordonnées"""
    print("\n=== TEST CAS LIMITES COORDONNÉES ===")
    
    edge_cases = [
        ("EDIT 1 [0,0]", "Coin supérieur gauche"),
        ("FILL 2 [9,9]", "Cellule avec coordonnées élevées"),
        ("CLEAR [0,0 0,0]", "Rectangle d'une seule cellule"),
        ("COPY [5,5 5,10]", "Rectangle horizontal (même ligne)"),
        ("PASTE [3,2]", "Cellule de destination pour paste")
    ]
    
    for command, description in edge_cases:
        print(f"Test: {command} - {description}")
        try:
            parsed = UnifiedCommand.parse(command)
            if parsed:
                coords = parsed.coordinates
                print(f"  Coordonnées: {coords}")
                
                if len(coords) == 2:
                    print(f"  Type: Cellule individuelle (ligne {coords[0]}, colonne {coords[1]})")
                elif len(coords) == 4:
                    print(f"  Type: Zone rectangulaire de ({coords[0]},{coords[1]}) à ({coords[2]},{coords[3]})")
                    
                    # Vérification de cohérence
                    if coords[0] <= coords[2] and coords[1] <= coords[3]:
                        print(f"  ✅ Rectangle valide")
                    else:
                        print(f"  ⚠️ Rectangle avec coordonnées inversées")
                        
                print(f"  ✅ Parsing réussi")
            else:
                print(f"  ❌ Parsing failed")
        except Exception as e:
            print(f"  ❌ Exception: {e}")
        print()

def test_multiple_coordinate_blocks():
    """Test des commandes avec plusieurs blocs de coordonnées"""
    print("\n=== TEST BLOCS MULTIPLES DE COORDONNÉES ===")
    
    # Note: Selon la documentation, certaines commandes peuvent avoir plusieurs blocs
    multi_block_cases = [
        "FILL 5 [1,2 3,4] [6,7 8,9]",  # Deux zones à remplir
        "CLEAR [0,0 2,2] [4,4 6,6]",   # Deux zones à effacer
    ]
    
    for command in multi_block_cases:
        print(f"Test: {command}")
        try:
            parsed = UnifiedCommand.parse(command)
            if parsed:
                coords = parsed.coordinates
                print(f"  Coordonnées totales: {coords}")
                
                # Analyser les blocs
                if len(coords) == 8:  # 2 rectangles
                    print(f"  Rectangle 1: de ({coords[0]},{coords[1]}) à ({coords[2]},{coords[3]})")
                    print(f"  Rectangle 2: de ({coords[4]},{coords[5]}) à ({coords[6]},{coords[7]})")
                    print(f"  ✅ Deux blocs rectangulaires détectés")
                else:
                    print(f"  ⚠️ Nombre de coordonnées inattendu: {len(coords)}")
                    
            else:
                print(f"  ❌ Parsing failed")
        except Exception as e:
            print(f"  ❌ Exception: {e}")
        print()

if __name__ == "__main__":
    test_coordinate_semantics()
    test_coordinate_edge_cases()
    test_multiple_coordinate_blocks()
    print("=== VALIDATION TERMINÉE ===")
    print("\n📋 RÉSUMÉ:")
    print("✅ Le format des coordonnées respecte FONCTIONNEMENT_GENERAL_COMMANDES_UNIFIEES.md")
    print("✅ [ligne,colonne] pour cellule individuelle")
    print("✅ [ligne1,colonne1 ligne2,colonne2] pour zone rectangulaire")
    print("✅ Support des blocs multiples de coordonnées")
