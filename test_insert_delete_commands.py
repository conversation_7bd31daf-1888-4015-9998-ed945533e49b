#!/usr/bin/env python3
"""
Test des commandes INSERT et DELETE implémentées
"""

import sys
import os
import numpy as np

# Ajouter le chemin du backend
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

try:
    from command_system.command_executor import CommandExecutor
    from command_system.unified_command import UnifiedCommand
except ImportError as e:
    print(f"Erreur d'import: {e}")
    sys.exit(1)

def print_grid(grid, title="Grille"):
    """Affiche une grille de manière lisible"""
    print(f"\n{title}:")
    if grid is None:
        print("  None")
        return
    
    for row in grid:
        print("  " + " ".join(str(cell) for cell in row))

def test_insert_commands():
    """Test des commandes INSERT"""
    print("=== TEST DES COMMANDES INSERT ===")
    
    executor = CommandExecutor()
    
    # Test 1: Initialiser une grille 3x3
    print("\n1. Initialisation d'une grille 3x3")
    result = executor.execute_commands(["INIT 3x3"])
    print(f"Succès: {result['success']}")
    if result['error']:
        print(f"Erreur: {result['error']}")
    print_grid(result['grid'], "Grille initiale")
    
    # Test 2: Remplir quelques cellules pour visualiser
    print("\n2. Remplissage de quelques cellules")
    result = executor.execute_commands([
        "INIT 3x3",
        "EDIT 1 [0,0]",
        "EDIT 2 [1,1]", 
        "EDIT 3 [2,2]"
    ])
    print(f"Succès: {result['success']}")
    if result['error']:
        print(f"Erreur: {result['error']}")
    print_grid(result['grid'], "Grille avec données")
    
    # Test 3: Insérer une ligne au début
    print("\n3. Insertion d'une ligne au début (ABOVE)")
    result = executor.execute_commands([
        "INIT 3x3",
        "EDIT 1 [0,0]",
        "EDIT 2 [1,1]", 
        "EDIT 3 [2,2]",
        "INSERT 1 ROWS ABOVE"
    ])
    print(f"Succès: {result['success']}")
    if result['error']:
        print(f"Erreur: {result['error']}")
    print_grid(result['grid'], "Après insertion ligne au début")
    
    # Test 4: Insérer une colonne à la fin
    print("\n4. Insertion d'une colonne à la fin (AFTER)")
    result = executor.execute_commands([
        "INIT 3x3",
        "EDIT 1 [0,0]",
        "EDIT 2 [1,1]", 
        "EDIT 3 [2,2]",
        "INSERT 1 COLUMNS AFTER"
    ])
    print(f"Succès: {result['success']}")
    if result['error']:
        print(f"Erreur: {result['error']}")
    print_grid(result['grid'], "Après insertion colonne à la fin")
    
    # Test 5: Insérer avec coordonnées spécifiques
    print("\n5. Insertion avec coordonnées spécifiques")
    result = executor.execute_commands([
        "INIT 4x4",
        "EDIT 1 [0,0]",
        "EDIT 2 [1,1]", 
        "EDIT 3 [2,2]",
        "EDIT 4 [3,3]",
        "INSERT 1 ROWS BELOW [1,1]"
    ])
    print(f"Succès: {result['success']}")
    if result['error']:
        print(f"Erreur: {result['error']}")
    print_grid(result['grid'], "Après insertion ligne avec coordonnées")

def test_delete_commands():
    """Test des commandes DELETE"""
    print("\n\n=== TEST DES COMMANDES DELETE ===")
    
    executor = CommandExecutor()
    
    # Test 1: Supprimer une ligne spécifique
    print("\n1. Suppression d'une ligne spécifique")
    result = executor.execute_commands([
        "INIT 4x4",
        "EDIT 1 [0,0]",
        "EDIT 2 [1,1]", 
        "EDIT 3 [2,2]",
        "EDIT 4 [3,3]",
        "DELETE ROWS [1,0 1,3]"  # Supprimer la ligne 1
    ])
    print(f"Succès: {result['success']}")
    if result['error']:
        print(f"Erreur: {result['error']}")
    print_grid(result['grid'], "Après suppression ligne 1")
    
    # Test 2: Supprimer une colonne spécifique
    print("\n2. Suppression d'une colonne spécifique")
    result = executor.execute_commands([
        "INIT 4x4",
        "EDIT 1 [0,0]",
        "EDIT 2 [1,1]", 
        "EDIT 3 [2,2]",
        "EDIT 4 [3,3]",
        "DELETE COLUMNS [0,2 3,2]"  # Supprimer la colonne 2
    ])
    print(f"Succès: {result['success']}")
    if result['error']:
        print(f"Erreur: {result['error']}")
    print_grid(result['grid'], "Après suppression colonne 2")
    
    # Test 3: Supprimer plusieurs lignes
    print("\n3. Suppression de plusieurs lignes")
    result = executor.execute_commands([
        "INIT 5x5",
        "EDIT 1 [0,0]",
        "EDIT 2 [1,1]", 
        "EDIT 3 [2,2]",
        "EDIT 4 [3,3]",
        "EDIT 5 [4,4]",
        "DELETE ROWS [1,0 2,4]"  # Supprimer les lignes 1 et 2
    ])
    print(f"Succès: {result['success']}")
    if result['error']:
        print(f"Erreur: {result['error']}")
    print_grid(result['grid'], "Après suppression lignes 1-2")

def test_error_cases():
    """Test des cas d'erreur"""
    print("\n\n=== TEST DES CAS D'ERREUR ===")
    
    executor = CommandExecutor()
    
    # Test 1: INSERT sans paramètres suffisants
    print("\n1. INSERT sans paramètres suffisants")
    result = executor.execute_commands(["INIT 3x3", "INSERT 1"])
    print(f"Succès: {result['success']}")
    print(f"Erreur attendue: {result['error']}")
    
    # Test 2: DELETE avec type invalide
    print("\n2. DELETE avec type invalide")
    result = executor.execute_commands(["INIT 3x3", "DELETE INVALID [0,0]"])
    print(f"Succès: {result['success']}")
    print(f"Erreur attendue: {result['error']}")
    
    # Test 3: DELETE sans coordonnées
    print("\n3. DELETE sans coordonnées")
    result = executor.execute_commands(["INIT 3x3", "DELETE ROWS"])
    print(f"Succès: {result['success']}")
    print(f"Erreur attendue: {result['error']}")

if __name__ == "__main__":
    test_insert_commands()
    test_delete_commands()
    test_error_cases()
    print("\n=== TESTS TERMINÉS ===")
