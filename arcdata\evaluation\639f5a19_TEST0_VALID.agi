TRANSFERT {INIT 23x23; EDIT 8 [1,3]; EDIT 8 [1,4]; EDIT 8 [1,5]; EDIT 8 [1,6]; EDIT 8 [1,7]; EDIT 8 [1,8]; EDIT 8 [2,3]; EDIT 8 [2,4]; EDIT 8 [2,5]; EDIT 8 [2,6]; EDIT 8 [2,7]; EDIT 8 [2,8]; EDIT 8 [3,3]; EDIT 8 [3,4]; EDIT 8 [3,5]; EDIT 8 [3,6]; EDIT 8 [3,7]; EDIT 8 [3,8]; EDIT 8 [4,3]; EDIT 8 [4,4]; EDIT 8 [4,5]; EDIT 8 [4,6]; EDIT 8 [4,7]; EDIT 8 [4,8]; EDIT 8 [4,12]; EDIT 8 [4,13]; EDIT 8 [4,14]; EDIT 8 [4,15]; EDIT 8 [4,16]; EDIT 8 [4,17]; EDIT 8 [4,18]; EDIT 8 [4,19]; EDIT 8 [4,20]; EDIT 8 [4,21]; EDIT 8 [5,3]; EDIT 8 [5,4]; EDIT 8 [5,5]; EDIT 8 [5,6]; EDIT 8 [5,7]; EDIT 8 [5,8]; EDIT 8 [5,12]; EDIT 8 [5,13]; EDIT 8 [5,14]; EDIT 8 [5,15]; EDIT 8 [5,16]; EDIT 8 [5,17]; EDIT 8 [5,18]; EDIT 8 [5,19]; EDIT 8 [5,20]; EDIT 8 [5,21]; EDIT 8 [6,3]; EDIT 8 [6,4]; EDIT 8 [6,5]; EDIT 8 [6,6]; EDIT 8 [6,7]; EDIT 8 [6,8]; EDIT 8 [6,12]; EDIT 8 [6,13]; EDIT 8 [6,14]; EDIT 8 [6,15]; EDIT 8 [6,16]; EDIT 8 [6,17]; EDIT 8 [6,18]; EDIT 8 [6,19]; EDIT 8 [6,20]; EDIT 8 [6,21]; EDIT 8 [7,3]; EDIT 8 [7,4]; EDIT 8 [7,5]; EDIT 8 [7,6]; EDIT 8 [7,7]; EDIT 8 [7,8]; EDIT 8 [7,12]; EDIT 8 [7,13]; EDIT 8 [7,14]; EDIT 8 [7,15]; EDIT 8 [7,16]; EDIT 8 [7,17]; EDIT 8 [7,18]; EDIT 8 [7,19]; EDIT 8 [7,20]; EDIT 8 [7,21]; EDIT 8 [8,3]; EDIT 8 [8,4]; EDIT 8 [8,5]; EDIT 8 [8,6]; EDIT 8 [8,7]; EDIT 8 [8,8]; EDIT 8 [8,12]; EDIT 8 [8,13]; EDIT 8 [8,14]; EDIT 8 [8,15]; EDIT 8 [8,16]; EDIT 8 [8,17]; EDIT 8 [8,18]; EDIT 8 [8,19]; EDIT 8 [8,20]; EDIT 8 [8,21]; EDIT 8 [9,3]; EDIT 8 [9,4]; EDIT 8 [9,5]; EDIT 8 [9,6]; EDIT 8 [9,7]; EDIT 8 [9,8]; EDIT 8 [9,12]; EDIT 8 [9,13]; EDIT 8 [9,14]; EDIT 8 [9,15]; EDIT 8 [9,16]; EDIT 8 [9,17]; EDIT 8 [9,18]; EDIT 8 [9,19]; EDIT 8 [9,20]; EDIT 8 [9,21]; EDIT 8 [10,3]; EDIT 8 [10,4]; EDIT 8 [10,5]; EDIT 8 [10,6]; EDIT 8 [10,7]; EDIT 8 [10,8]; EDIT 8 [10,12]; EDIT 8 [10,13]; EDIT 8 [10,14]; EDIT 8 [10,15]; EDIT 8 [10,16]; EDIT 8 [10,17]; EDIT 8 [10,18]; EDIT 8 [10,19]; EDIT 8 [10,20]; EDIT 8 [10,21]; EDIT 8 [11,12]; EDIT 8 [11,13]; EDIT 8 [11,14]; EDIT 8 [11,15]; EDIT 8 [11,16]; EDIT 8 [11,17]; EDIT 8 [11,18]; EDIT 8 [11,19]; EDIT 8 [11,20]; EDIT 8 [11,21]; EDIT 8 [12,12]; EDIT 8 [12,13]; EDIT 8 [12,14]; EDIT 8 [12,15]; EDIT 8 [12,16]; EDIT 8 [12,17]; EDIT 8 [12,18]; EDIT 8 [12,19]; EDIT 8 [12,20]; EDIT 8 [12,21]; EDIT 8 [13,12]; EDIT 8 [13,13]; EDIT 8 [13,14]; EDIT 8 [13,15]; EDIT 8 [13,16]; EDIT 8 [13,17]; EDIT 8 [13,18]; EDIT 8 [13,19]; EDIT 8 [13,20]; EDIT 8 [13,21]; EDIT 8 [14,12]; EDIT 8 [14,13]; EDIT 8 [14,14]; EDIT 8 [14,15]; EDIT 8 [14,16]; EDIT 8 [14,17]; EDIT 8 [14,18]; EDIT 8 [14,19]; EDIT 8 [14,20]; EDIT 8 [14,21]; EDIT 8 [15,3]; EDIT 8 [15,4]; EDIT 8 [15,5]; EDIT 8 [15,6]; EDIT 8 [15,7]; EDIT 8 [15,8]; EDIT 8 [15,12]; EDIT 8 [15,13]; EDIT 8 [15,14]; EDIT 8 [15,15]; EDIT 8 [15,16]; EDIT 8 [15,17]; EDIT 8 [15,18]; EDIT 8 [15,19]; EDIT 8 [15,20]; EDIT 8 [15,21]; EDIT 8 [16,3]; EDIT 8 [16,4]; EDIT 8 [16,5]; EDIT 8 [16,6]; EDIT 8 [16,7]; EDIT 8 [16,8]; EDIT 8 [16,12]; EDIT 8 [16,13]; EDIT 8 [16,14]; EDIT 8 [16,15]; EDIT 8 [16,16]; EDIT 8 [16,17]; EDIT 8 [16,18]; EDIT 8 [16,19]; EDIT 8 [16,20]; EDIT 8 [16,21]; EDIT 8 [17,3]; EDIT 8 [17,4]; EDIT 8 [17,5]; EDIT 8 [17,6]; EDIT 8 [17,7]; EDIT 8 [17,8]; EDIT 8 [17,12]; EDIT 8 [17,13]; EDIT 8 [17,14]; EDIT 8 [17,15]; EDIT 8 [17,16]; EDIT 8 [17,17]; EDIT 8 [17,18]; EDIT 8 [17,19]; EDIT 8 [17,20]; EDIT 8 [17,21]; EDIT 8 [18,3]; EDIT 8 [18,4]; EDIT 8 [18,5]; EDIT 8 [18,6]; EDIT 8 [18,7]; EDIT 8 [18,8]; EDIT 8 [19,3]; EDIT 8 [19,4]; EDIT 8 [19,5]; EDIT 8 [19,6]; EDIT 8 [19,7]; EDIT 8 [19,8]; EDIT 8 [20,3]; EDIT 8 [20,4]; EDIT 8 [20,5]; EDIT 8 [20,6]; EDIT 8 [20,7]; EDIT 8 [20,8]}
FILL 4 [3,5 8,6]
FILL 4 [6,14 15,19]
FILL 4 [17,5 18,6]
EDITS {EDIT 2 [11,12]; EDIT 2 [11,13]; EDIT 2 [16,16]; EDIT 2 [17,16]}
FLOODFILL 2 [16,13]
EDITS {EDIT 2 [18,3]; EDIT 2 [18,4]; EDIT 2 [19,5]; EDIT 2 [20,5]; EDIT 2 [6,3]; EDIT 2 [6,4]; EDIT 2 [9,5]; EDIT 2 [10,5]}
FLOODFILLS {FLOODFILL 2 [10,4]; FLOODFILL 2 [20,3]; FLOODFILL 3 [1,5]}
EDITS {EDIT 1 [1,6]; EDIT 1 [2,6]; EDIT 1 [5,7]; EDIT 1 [5,8]}
FLOODFILLS {FLOODFILL 1 [2,8]; FLOODFILL 6 [2,4]}
EDITS {EDIT 1 [5,17]; EDIT 1 [4,17]; EDIT 1 [10,20]; EDIT 1 [10,21]; EDIT 1 [16,6]; EDIT 1 [15,6]; EDIT 1 [17,7]; EDIT 1 [17,8]}
FLOODFILLS {FLOODFILL 1 [16,8]; FLOODFILL 1 [5,21]; FLOODFILL 3 [20,8]; FLOODFILL 3 [17,21]; FLOODFILL 6 [4,13]; FLOODFILL 6 [16,4]}
END