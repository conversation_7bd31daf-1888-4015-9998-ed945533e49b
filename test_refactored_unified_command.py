#!/usr/bin/env python3
"""
Test de la version refactorisée de UnifiedCommand
"""

import sys
import os

# Ajouter le chemin du backend
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

try:
    from command_system.unified_command import UnifiedCommand
except ImportError as e:
    print(f"Erreur d'import: {e}")
    sys.exit(1)

def test_modern_commands():
    """Test des commandes modernes supportées"""
    print("=== TEST COMMANDES MODERNES ===")
    
    modern_commands = [
        "INIT 3x3",
        "EDIT 7 [0,0]",
        "FILL 5 [1,2 3,4]",
        "CLEAR [0,0 2,2]",
        "INSERT 2 ROWS ABOVE [1,1]",
        "DELETE COLUMNS [0,1 2,1]",
        "FLIP HORIZONTAL ([1,2 3,4])",
        "ROTATE LEFT ([0,0 2,2])",
        "TRANSFERT {INIT 3x3; EDIT 7 [0,0]}",
        "END"
    ]
    
    for cmd_str in modern_commands:
        try:
            parsed = UnifiedCommand.parse(cmd_str)
            if parsed:
                print(f"✅ {cmd_str}")
                print(f"   Action: {parsed.action}")
                print(f"   Parameters: {parsed.parameters}")
                print(f"   Coordinates: {parsed.coordinates}")
                print(f"   Valid: {parsed.validate_syntax()}")
            else:
                print(f"❌ {cmd_str} - Parsing failed")
        except Exception as e:
            print(f"❌ {cmd_str} - Exception: {e}")
        print()

def test_obsolete_commands():
    """Test que les commandes obsolètes sont rejetées"""
    print("\n=== TEST REJET COMMANDES OBSOLÈTES ===")
    
    obsolete_commands = [
        "ROTATE_RIGHT [0,0 2,2]",
        "FLIP_HORIZONTAL [1,1 3,3]",
        "INSERT_ROW 1",
        "DELETE_COL 2",
        "SELECT_COLOR 1,2,3 [0,0 4,4]",
        "SELECT_INVERT [1,1 3,3]",
        "PROPOSE",
        "VALIDATE"
    ]
    
    for cmd_str in obsolete_commands:
        try:
            parsed = UnifiedCommand.parse(cmd_str)
            if parsed:
                print(f"⚠️ {cmd_str} - Devrait être rejeté mais a été parsé")
            else:
                print(f"✅ {cmd_str} - Correctement rejeté")
        except Exception as e:
            print(f"✅ {cmd_str} - Exception attendue: {e}")

def test_parameter_parsing():
    """Test du parsing des paramètres"""
    print("\n=== TEST PARSING PARAMÈTRES ===")
    
    test_cases = [
        ("INIT 5x7", ["INIT", [5, 7], []]),
        ("EDIT 3 [1,2]", ["EDIT", [3], [1, 2]]),
        ("REPLACE 1,2,3 5 [0,0 4,4]", ["REPLACE", [[1, 2, 3], 5], [0, 0, 4, 4]]),
        ("INSERT 2 ROWS ABOVE", ["INSERT", [2, "ROWS", "ABOVE"], []]),
        ("DELETE COLUMNS [1,0 1,3]", ["DELETE", ["COLUMNS"], [1, 0, 1, 3]])
    ]
    
    for cmd_str, expected in test_cases:
        try:
            parsed = UnifiedCommand.parse(cmd_str)
            if parsed:
                print(f"Command: {cmd_str}")
                print(f"  Expected action: {expected[0]}, Got: {parsed.action}")
                print(f"  Expected params: {expected[1]}, Got: {parsed.parameters}")
                print(f"  Expected coords: {expected[2]}, Got: {parsed.coordinates}")
                
                action_ok = parsed.action == expected[0]
                # Note: Les paramètres peuvent être parsés différemment selon la logique
                coords_ok = parsed.coordinates == expected[2]
                
                if action_ok and coords_ok:
                    print(f"  ✅ Parsing correct")
                else:
                    print(f"  ⚠️ Différences détectées")
            else:
                print(f"❌ {cmd_str} - Parsing failed")
        except Exception as e:
            print(f"❌ {cmd_str} - Exception: {e}")
        print()

def test_coordinate_formats():
    """Test des différents formats de coordonnées"""
    print("\n=== TEST FORMATS COORDONNÉES ===")
    
    coord_tests = [
        ("EDIT 5 [1,2]", [1, 2]),
        ("FILL 3 [0,0 3,3]", [0, 0, 3, 3]),
        ("CLEAR [1,1 2,2]", [1, 1, 2, 2])
    ]
    
    for cmd_str, expected_coords in coord_tests:
        try:
            parsed = UnifiedCommand.parse(cmd_str)
            if parsed:
                print(f"Command: {cmd_str}")
                print(f"  Expected: {expected_coords}")
                print(f"  Got: {parsed.coordinates}")
                if parsed.coordinates == expected_coords:
                    print(f"  ✅ Coordonnées correctes")
                else:
                    print(f"  ⚠️ Coordonnées différentes")
            else:
                print(f"❌ {cmd_str} - Parsing failed")
        except Exception as e:
            print(f"❌ {cmd_str} - Exception: {e}")
        print()

if __name__ == "__main__":
    test_modern_commands()
    test_obsolete_commands()
    test_parameter_parsing()
    test_coordinate_formats()
    print("\n=== TESTS TERMINÉS ===")
