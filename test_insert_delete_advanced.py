#!/usr/bin/env python3
"""
Tests avancés des commandes INSERT et DELETE
"""

import sys
import os
import numpy as np

# Ajouter le chemin du backend
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

try:
    from command_system.command_executor import CommandExecutor
    from command_system.unified_command import UnifiedCommand
except ImportError as e:
    print(f"Erreur d'import: {e}")
    sys.exit(1)

def print_grid(grid, title="Grille"):
    """Affiche une grille de manière lisible"""
    print(f"\n{title}:")
    if grid is None:
        print("  None")
        return
    
    for row in grid:
        print("  " + " ".join(str(cell) for cell in row))

def test_complex_scenarios():
    """Test de scénarios complexes"""
    print("=== TESTS AVANCÉS INSERT/DELETE ===")
    
    executor = CommandExecutor()
    
    # Test 1: Créer un motif, insérer des lignes, puis supprimer des colonnes
    print("\n1. Scénario complexe: motif + insertions + suppressions")
    result = executor.execute_commands([
        "INIT 5x5",
        "EDIT 1 [0,0]", "EDIT 1 [0,4]",  # Coins supérieurs
        "EDIT 1 [4,0]", "EDIT 1 [4,4]",  # Coins inférieurs
        "EDIT 2 [2,2]",                   # Centre
        "EDIT 3 [1,1]", "EDIT 3 [1,3]",  # Ligne 1
        "EDIT 3 [3,1]", "EDIT 3 [3,3]",  # Ligne 3
    ])
    print(f"Succès: {result['success']}")
    if result['error']:
        print(f"Erreur: {result['error']}")
    print_grid(result['grid'], "Motif initial 5x5")
    
    # Insérer une ligne au milieu
    result = executor.execute_commands([
        "INIT 5x5",
        "EDIT 1 [0,0]", "EDIT 1 [0,4]",
        "EDIT 1 [4,0]", "EDIT 1 [4,4]",
        "EDIT 2 [2,2]",
        "EDIT 3 [1,1]", "EDIT 3 [1,3]",
        "EDIT 3 [3,1]", "EDIT 3 [3,3]",
        "INSERT 1 ROWS BELOW [2,2]"  # Insérer après la ligne du centre
    ])
    print(f"Succès: {result['success']}")
    if result['error']:
        print(f"Erreur: {result['error']}")
    print_grid(result['grid'], "Après insertion ligne au centre")
    
    # Supprimer une colonne
    result = executor.execute_commands([
        "INIT 5x5",
        "EDIT 1 [0,0]", "EDIT 1 [0,4]",
        "EDIT 1 [4,0]", "EDIT 1 [4,4]",
        "EDIT 2 [2,2]",
        "EDIT 3 [1,1]", "EDIT 3 [1,3]",
        "EDIT 3 [3,1]", "EDIT 3 [3,3]",
        "INSERT 1 ROWS BELOW [2,2]",
        "DELETE COLUMNS [0,1 5,1]"  # Supprimer la colonne 1
    ])
    print(f"Succès: {result['success']}")
    if result['error']:
        print(f"Erreur: {result['error']}")
    print_grid(result['grid'], "Après suppression colonne 1")

def test_multiple_operations():
    """Test d'opérations multiples"""
    print("\n\n=== TESTS OPÉRATIONS MULTIPLES ===")
    
    executor = CommandExecutor()
    
    # Test 1: Insérer plusieurs lignes d'un coup
    print("\n1. Insertion de plusieurs lignes")
    result = executor.execute_commands([
        "INIT 3x3",
        "EDIT 1 [0,0]", "EDIT 2 [1,1]", "EDIT 3 [2,2]",
        "INSERT 2 ROWS ABOVE"  # Insérer 2 lignes au début
    ])
    print(f"Succès: {result['success']}")
    if result['error']:
        print(f"Erreur: {result['error']}")
    print_grid(result['grid'], "Après insertion de 2 lignes")
    
    # Test 2: Insérer plusieurs colonnes
    print("\n2. Insertion de plusieurs colonnes")
    result = executor.execute_commands([
        "INIT 3x3",
        "EDIT 1 [0,0]", "EDIT 2 [1,1]", "EDIT 3 [2,2]",
        "INSERT 3 COLUMNS AFTER"  # Insérer 3 colonnes à la fin
    ])
    print(f"Succès: {result['success']}")
    if result['error']:
        print(f"Erreur: {result['error']}")
    print_grid(result['grid'], "Après insertion de 3 colonnes")
    
    # Test 3: Supprimer plusieurs lignes avec plage
    print("\n3. Suppression de plage de lignes")
    result = executor.execute_commands([
        "INIT 6x4",
        "EDIT 1 [0,0]", "EDIT 2 [1,1]", "EDIT 3 [2,2]", 
        "EDIT 4 [3,3]", "EDIT 5 [4,0]", "EDIT 6 [5,1]",
        "DELETE ROWS [1,0 3,3]"  # Supprimer les lignes 1, 2, 3
    ])
    print(f"Succès: {result['success']}")
    if result['error']:
        print(f"Erreur: {result['error']}")
    print_grid(result['grid'], "Après suppression lignes 1-3")

def test_edge_cases():
    """Test des cas limites"""
    print("\n\n=== TESTS CAS LIMITES ===")
    
    executor = CommandExecutor()
    
    # Test 1: Insérer dans une grille 1x1
    print("\n1. Insertion dans grille 1x1")
    result = executor.execute_commands([
        "INIT 1x1",
        "EDIT 5 [0,0]",
        "INSERT 1 ROWS BELOW [0,0]"
    ])
    print(f"Succès: {result['success']}")
    if result['error']:
        print(f"Erreur: {result['error']}")
    print_grid(result['grid'], "Grille 1x1 après insertion ligne")
    
    # Test 2: Essayer de supprimer la dernière ligne (doit échouer)
    print("\n2. Tentative suppression dernière ligne")
    result = executor.execute_commands([
        "INIT 1x3",
        "EDIT 5 [0,0]",
        "DELETE ROWS [0,0 0,2]"
    ])
    print(f"Succès: {result['success']}")
    print(f"Erreur attendue: {result['error']}")
    
    # Test 3: Essayer de supprimer la dernière colonne (doit échouer)
    print("\n3. Tentative suppression dernière colonne")
    result = executor.execute_commands([
        "INIT 3x1",
        "EDIT 5 [0,0]",
        "DELETE COLUMNS [0,0 2,0]"
    ])
    print(f"Succès: {result['success']}")
    print(f"Erreur attendue: {result['error']}")
    
    # Test 4: Coordonnées hors limites
    print("\n4. Coordonnées hors limites")
    result = executor.execute_commands([
        "INIT 3x3",
        "DELETE ROWS [5,0 5,2]"  # Ligne 5 n'existe pas
    ])
    print(f"Succès: {result['success']}")
    print(f"Erreur attendue: {result['error']}")

def test_format_variations():
    """Test des variations de format"""
    print("\n\n=== TESTS VARIATIONS DE FORMAT ===")
    
    executor = CommandExecutor()
    
    # Test 1: INSERT avec différentes positions
    print("\n1. INSERT avec différentes positions")
    positions = ['ABOVE', 'BELOW', 'BEFORE', 'AFTER']
    
    for pos in positions:
        if pos in ['ABOVE', 'BELOW']:
            cmd = f"INSERT 1 ROWS {pos}"
            element = "ligne"
        else:
            cmd = f"INSERT 1 COLUMNS {pos}"
            element = "colonne"
            
        result = executor.execute_commands([
            "INIT 3x3",
            "EDIT 1 [1,1]",
            cmd
        ])
        print(f"  {cmd}: Succès={result['success']}")
        if result['error']:
            print(f"    Erreur: {result['error']}")
        else:
            print(f"    Nouvelle taille: {len(result['grid'])}x{len(result['grid'][0]) if result['grid'] else 0}")

if __name__ == "__main__":
    test_complex_scenarios()
    test_multiple_operations()
    test_edge_cases()
    test_format_variations()
    print("\n=== TOUS LES TESTS TERMINÉS ===")
