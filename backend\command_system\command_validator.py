"""
CommandValidator - Version Unifiée Pure
"""

from typing import List, Dict, Any
from .unified_command import UnifiedCommand
from .command_executor import CommandExecutor


class CommandValidator:
    """Validateur pour les commandes unifiées"""

    def __init__(self):
        """Initialise le validateur"""
        pass

    def validate_commands(self, commands: List[str], task_data=None) -> Dict[str, Any]:
        """
        Valide une liste de commandes d'automatisation au format unifié
        
        Args:
            commands: Liste de commandes au format unifié
            task_data: Données de la tâche pour validation optionnelle
            
        Returns:
            Dict contenant valid, valid_commands, errors, execution_result, validation_result
        """
        valid_commands = []
        errors = []

        # Vérifier la syntaxe de chaque commande
        for i, command in enumerate(commands):
            try:
                # Ignorer les lignes vides et commentaires
                if not command.strip() or command.strip().startswith('#'):
                    continue
                
                # Parser avec UnifiedCommand
                unified_cmd = UnifiedCommand.parse(command)
                if unified_cmd and unified_cmd.validate_syntax():
                    valid_commands.append(command)
                else:
                    errors.append({
                        'line': i + 1,
                        'command': command,
                        'error': 'Syntaxe invalide selon le format unifié'
                    })
            except Exception as e:
                errors.append({
                    'line': i + 1,
                    'command': command,
                    'error': f'Erreur de parsing: {str(e)}'
                })

        # Si des erreurs de syntaxe ont été trouvées, retourner immédiatement
        if errors:
            return {
                'valid': False,
                'valid_commands': valid_commands,
                'errors': errors,
                'execution_result': None,
                'validation_result': None
            }        # Exécuter les commandes valides
        # Utiliser notre CommandExecutor mis à jour qui supporte les sélections spéciales
        executor = CommandExecutor()
        
        # Si des données de tâche sont fournies, initialiser la grille avec l'entrée
        if task_data and 'test' in task_data and task_data['test'] and 'input' in task_data['test'][0]:
            import numpy as np
            input_grid = np.array(task_data['test'][0]['input'])
            executor.grid = input_grid
            executor.height = len(input_grid)
            executor.width = len(input_grid[0]) if len(input_grid) > 0 else 0
        
        execution_result = executor.execute_commands(valid_commands)

        # Si l'exécution a échoué, ajouter l'erreur
        if not execution_result['success']:
            errors.append({
                'line': -1,  # Ligne inconnue
                'command': '',
                'error': execution_result['error']
            })        # Si des données de tâche sont fournies, valider la solution
        validation_result = None
        if task_data and execution_result['success']:
            # Récupérer la sortie attendue du premier exemple de test
            if 'test' in task_data and task_data['test'] and 'output' in task_data['test'][0]:
                expected_output = task_data['test'][0]['output']
                generated_grid = execution_result.get('grid')
                
                # Comparaison détaillée des grilles
                comparison_details = self._compare_grids(generated_grid, expected_output)
                
                if comparison_details['is_identical']:
                    validation_result = {'success': True}
                else:
                    # Générer une erreur GRID_MISMATCH avec détails
                    matching_cells = comparison_details['matching_cells']
                    total_cells = comparison_details['total_cells_expected']
                    percentage = (matching_cells / total_cells * 100) if total_cells > 0 else 0
                    
                    error_message = f"GRID_MISMATCH: La grille générée ne correspond pas à la sortie attendue. Cellules correspondantes: {matching_cells}/{total_cells} ({percentage:.1f}%)"
                    validation_result = {'success': False, 'error': error_message}
                    
                    errors.append({
                        'line': -1,
                        'command': '',
                        'error': error_message,
                        'error_type': 'GRID_MISMATCH',
                        'matching_cells': matching_cells,
                        'total_cells_expected': total_cells,
                        'total_cells_generated': comparison_details['total_cells_generated'],
                        'generated_grid': generated_grid,
                        'expected_grid': expected_output
                    })

        return {
            'valid': len(errors) == 0,
            'valid_commands': valid_commands,
            'errors': errors,
            'execution_result': execution_result,
            'validation_result': validation_result
        }

    def validate_single_command(self, command: str) -> Dict[str, Any]:
        """
        Valide une commande individuelle
        
        Args:
            command: Commande au format unifié
            
        Returns:
            Dict contenant valid, unified_command, error
        """
        try:
            # Ignorer les lignes vides et commentaires
            if not command.strip() or command.strip().startswith('#'):
                return {
                    'valid': True,
                    'unified_command': None,
                    'error': None
                }
            
            # Parser avec UnifiedCommand
            unified_cmd = UnifiedCommand.parse(command)
            if unified_cmd and unified_cmd.validate_syntax():
                return {
                    'valid': True,
                    'unified_command': unified_cmd,
                    'error': None
                }
            else:
                return {
                    'valid': False,
                    'unified_command': unified_cmd,
                    'error': 'Syntaxe invalide selon le format unifié'
                }
        except Exception as e:
            return {
                'valid': False,
                'unified_command': None,
                'error': f'Erreur de parsing: {str(e)}'
            }

    def _compare_grids(self, generated_grid: List[List[int]], expected_grid: List[List[int]]) -> Dict[str, Any]:
        """
        Compare deux grilles et retourne des détails sur la comparaison.
        
        Args:
            generated_grid: Grille générée par l'exécution du scénario.
            expected_grid: Grille de sortie attendue pour le test.
            
        Returns:
            Un dictionnaire avec les détails de la comparaison:
            - 'is_identical': bool
            - 'matching_cells': int
            - 'total_cells_expected': int
            - 'total_cells_generated': int
            - 'diff_points': List[Dict] - Liste des points de différence (optionnel, pour logs détaillés)
        """
        details = {
            'is_identical': False,
            'matching_cells': 0,
            'total_cells_expected': 0,
            'total_cells_generated': 0,
            'diff_points': []  # Pourrait être utilisé pour loguer les différences exactes
        }

        # Calculer le nombre total de cellules pour la grille générée
        if generated_grid:
            details['total_cells_generated'] = sum(len(row) for row in generated_grid)
        
        # Calculer le nombre total de cellules pour la grille attendue
        if expected_grid:
            details['total_cells_expected'] = sum(len(row) for row in expected_grid)

        # Vérification initiale des dimensions
        if not generated_grid and not expected_grid:
            details['is_identical'] = True
            return details
        if not generated_grid or not expected_grid:  # L'une est vide, l'autre non
            return details
        
        gen_h, exp_h = len(generated_grid), len(expected_grid)
        if gen_h == 0 and exp_h == 0:  # Les deux sont des listes vides de lignes
            details['is_identical'] = True
            return details
        if gen_h == 0 or exp_h == 0:  # L'une a des lignes, l'autre non
            return details

        gen_w, exp_w = len(generated_grid[0]) if gen_h > 0 else 0, len(expected_grid[0]) if exp_h > 0 else 0
        
        if gen_h != exp_h or gen_w != exp_w:
            # Les dimensions ne correspondent pas, compter les correspondances sur la zone commune
            min_h = min(gen_h, exp_h)
            min_w = min(gen_w, exp_w)
            matching_count = 0
            for r in range(min_h):
                for c in range(min_w):
                    if generated_grid[r][c] == expected_grid[r][c]:
                        matching_count += 1
            details['matching_cells'] = matching_count
            return details  # is_identical reste False

        # Les dimensions sont identiques, comparer cellule par cellule
        matching_count = 0
        is_fully_identical = True
        for r in range(exp_h):
            for c in range(exp_w):
                if generated_grid[r][c] == expected_grid[r][c]:
                    matching_count += 1
                else:
                    is_fully_identical = False
        
        details['matching_cells'] = matching_count
        details['is_identical'] = is_fully_identical
        
        return details

    def get_command_info(self, command: str) -> Dict[str, Any]:
        """
        Retourne des informations détaillées sur une commande
        
        Args:
            command: Commande au format unifié
            
        Returns:
            Dict contenant les informations de la commande
        """
        try:
            unified_cmd = UnifiedCommand.parse(command)
            if not unified_cmd:
                return {
                    'valid': False,
                    'error': 'Impossible de parser la commande'
                }
            
            return {
                'valid': unified_cmd.validate_syntax(),
                'action': unified_cmd.action,
                'parameters': unified_cmd.parameters,
                'coordinates': unified_cmd.coordinates,
                'raw_command': unified_cmd.raw_command,
                'dict': unified_cmd.to_dict(),
                'error': None if unified_cmd.validate_syntax() else 'Syntaxe invalide'
            }
        except Exception as e:
            return {
                'valid': False,
                'error': f'Erreur: {str(e)}'
            }

    def validate_semantic(self, commands: List[str]) -> Dict[str, Any]:
        """
        Valide la sémantique d'une séquence de commandes
        
        Args:
            commands: Liste de commandes au format unifié
            
        Returns:
            Dict contenant les erreurs sémantiques trouvées
        """
        errors = []
        has_init = False
        has_propose_or_end = False
        
        for i, command in enumerate(commands):
            if not command.strip() or command.strip().startswith('#'):
                continue
                
            unified_cmd = UnifiedCommand.parse(command)
            if not unified_cmd:
                continue
            
            action = unified_cmd.action
            
            # Vérifier que INIT est la première commande utile
            if action == 'INIT':
                if has_init:
                    errors.append({
                        'line': i + 1,
                        'command': command,
                        'error': 'Multiples commandes INIT trouvées'
                    })
                has_init = True
            elif not has_init and action not in ['TRANSFERT']:
                errors.append({
                    'line': i + 1,
                    'command': command,
                    'error': 'Commande avant INIT (sauf TRANSFERT)'
                })
            
            # Vérifier la présence de PROPOSE ou END
            if action in ['PROPOSE', 'END']:
                has_propose_or_end = True
            
            # Vérifications spécifiques par commande
            if action == 'EDIT' and len(unified_cmd.coordinates) == 2:
                x, y = unified_cmd.coordinates
                if x < 0 or y < 0:
                    errors.append({
                        'line': i + 1,
                        'command': command,
                        'error': 'Coordonnées négatives'
                    })
            
            # Vérifier que les commandes avec parenthèses sont bien formées
            if '(' in command and ')' in command:
                if action not in ['FLIP HORIZONTAL', 'FLIP VERTICAL', 'ROTATE LEFT', 'ROTATE RIGHT',
                                'COPY', 'CUT', 'PASTE']:
                    errors.append({
                        'line': i + 1,
                        'command': command,
                        'error': 'Parenthèses dans une commande qui ne les supporte pas'
                    })
            
            # Vérifier que les commandes avec accolades sont bien formées
            if '{' in command and '}' in command:
                if action != 'TRANSFERT':
                    errors.append({
                        'line': i + 1,
                        'command': command,
                        'error': 'Accolades dans une commande autre que TRANSFERT'
                    })
        
        # Vérifier qu'il y a une commande de fin
        if not has_propose_or_end and has_init:
            errors.append({
                'line': -1,
                'command': '',
                'error': 'Aucune commande PROPOSE ou END trouvée'
            })
        
        return {
            'valid': len(errors) == 0,
            'errors': errors,
            'has_init': has_init,
            'has_end': has_propose_or_end
        }

    def suggest_corrections(self, command: str) -> List[str]:
        """
        Suggère des corrections pour une commande invalide
        
        Args:
            command: Commande invalide
            
        Returns:
            Liste de suggestions de correction
        """
        suggestions = []
        
        if not command.strip():
            return suggestions
        
        # Tentatives de corrections courantes
        
        # 1. Correction des formats incorrects vers format moderne
        if 'EDIT' in command:
            # EDIT x y value -> EDIT value [x,y] (format moderne)
            parts = command.strip().split()
            if len(parts) == 4 and parts[0].upper() == 'EDIT':
                try:
                    x, y, value = parts[1], parts[2], parts[3]
                    suggestions.append(f"EDIT {value} [{x},{y}]")
                except:
                    pass
        
        if 'INIT' in command:
            # INIT width height -> INIT widthxheight
            parts = command.strip().split()
            if len(parts) == 3 and parts[0].upper() == 'INIT':
                try:
                    width, height = parts[1], parts[2]
                    suggestions.append(f"INIT {width}x{height}")
                except:
                    pass
        
        if 'FLOODFILL' in command:
            # FLOODFILL color x y -> FLOODFILL color [x,y]
            parts = command.strip().split()
            if len(parts) == 4 and parts[0].upper() == 'FLOODFILL':
                try:
                    color, x, y = parts[1], parts[2], parts[3]
                    suggestions.append(f"FLOODFILL {color} [{x},{y}]")
                except:
                    pass
        
        # 2. Corrections des coordonnées
        if '[' in command and ',' not in command:
            # [x y] -> [x,y]
            import re
            corrected = re.sub(r'\[(\d+)\s+(\d+)\]', r'[\1,\2]', command)
            if corrected != command:
                suggestions.append(corrected)
        
        # 3. Correction des actions inconnues
        action = command.strip().split()[0].upper() if command.strip() else ''
        # Actions supportées - incluant RESIZE pour compatibilité avec scénarios existants
        known_actions = [
            'INIT', 'EDIT', 'FILL', 'CLEAR', 'SURROUND', 'REPLACE',
            'INSERT', 'DELETE', 'EXTRACT',
            'FLIP HORIZONTAL', 'FLIP VERTICAL', 'ROTATE LEFT', 'ROTATE RIGHT',
            'COPY', 'CUT', 'PASTE',
            'TRANSFERT', 'RESIZE', 'END'
        ]
        
        if action and action not in known_actions:
            # Chercher des actions similaires
            for known in known_actions:
                if action in known or known in action:
                    corrected = command.replace(action, known, 1)
                    suggestions.append(corrected)
                    break
        
        return suggestions

    def validate(self, commands_text: str) -> Dict[str, Any]:
        """
        Valide des commandes d'automatisation à partir d'un texte
        
        Args:
            commands_text: Texte contenant les commandes séparées par des nouvelles lignes
            
        Returns:
            Dict contenant valid, commands, errors
        """
        commands = [line.strip() for line in commands_text.strip().split('\n') if line.strip()]
        return self.validate_commands(commands)
