#!/usr/bin/env python3
"""
Test du CommandExecutor refactorisé - vérification que les commandes obsolètes sont rejetées
"""

import sys
import os

# Ajouter le chemin du backend
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

try:
    from command_system.command_executor import CommandExecutor
    from command_system.unified_command import UnifiedCommand
except ImportError as e:
    print(f"Erreur d'import: {e}")
    sys.exit(1)

def test_modern_commands_work():
    """Test que les commandes modernes fonctionnent"""
    print("=== TEST COMMANDES MODERNES FONCTIONNENT ===")
    
    executor = CommandExecutor()
    
    modern_commands = [
        "INIT 3x3",
        "EDIT 7 [0,0]",
        "FILL 5 [1,1 2,2]",
        "CLEAR [0,0 1,1]",
        "INSERT 1 ROWS ABOVE",
        "DELETE COLUMNS [0,0 3,0]",
        "END"
    ]
    
    for cmd_str in modern_commands:
        result = executor.execute_commands([cmd_str])
        if result['success']:
            print(f"✅ {cmd_str} - Fonctionne")
        else:
            print(f"❌ {cmd_str} - Erreur: {result['error']}")

def test_obsolete_commands_rejected():
    """Test que les commandes obsolètes sont rejetées"""
    print("\n=== TEST COMMANDES OBSOLÈTES REJETÉES ===")
    
    executor = CommandExecutor()
    
    # Initialiser une grille pour les tests
    executor.execute_commands(["INIT 3x3"])
    
    obsolete_commands = [
        "RESIZE 5x5",           # Commande RESIZE obsolète
        "SELECT_RELEASE",       # Commande SELECT_RELEASE obsolète
        "EDITS {EDIT 1 [0,0]}", # Commande EDITS obsolète
        "PROPOSE",              # Commande PROPOSE obsolète (si elle existait)
        "VALIDATE"              # Commande VALIDATE obsolète (si elle existait)
    ]
    
    for cmd_str in obsolete_commands:
        try:
            # D'abord, vérifier que le parsing rejette la commande
            parsed = UnifiedCommand.parse(cmd_str)
            if parsed is None:
                print(f"✅ {cmd_str} - Rejeté au parsing")
                continue
            
            # Si le parsing passe, vérifier que l'exécution échoue
            result = executor.execute_commands([cmd_str])
            if not result['success']:
                print(f"✅ {cmd_str} - Rejeté à l'exécution: {result['error']}")
            else:
                print(f"⚠️ {cmd_str} - Devrait être rejeté mais a réussi")
                
        except Exception as e:
            print(f"✅ {cmd_str} - Exception attendue: {e}")

def test_no_legacy_methods():
    """Test qu'il n'y a plus de méthodes legacy dans CommandExecutor"""
    print("\n=== TEST ABSENCE MÉTHODES LEGACY ===")
    
    executor = CommandExecutor()
    
    # Méthodes qui ne devraient plus exister
    legacy_methods = [
        '_cmd_edits',
        '_cmd_resize', 
        '_cmd_select_release',
        '_create_mask_from_special_selection'
    ]
    
    for method_name in legacy_methods:
        if hasattr(executor, method_name):
            print(f"⚠️ Méthode legacy trouvée: {method_name}")
        else:
            print(f"✅ Méthode legacy supprimée: {method_name}")

def test_modern_coordinate_format():
    """Test que le format moderne des coordonnées fonctionne"""
    print("\n=== TEST FORMAT MODERNE COORDONNÉES ===")
    
    executor = CommandExecutor()
    
    coordinate_tests = [
        ("INIT 4x4", "Initialisation"),
        ("EDIT 5 [2,3]", "Cellule individuelle [ligne,colonne]"),
        ("FILL 7 [0,0 2,2]", "Zone rectangulaire [ligne1,colonne1 ligne2,colonne2]"),
        ("CLEAR [1,1 3,3]", "Zone rectangulaire pour effacement"),
        ("INSERT 1 ROWS BELOW [1,1]", "Insertion avec coordonnées"),
        ("DELETE COLUMNS [0,1 3,1]", "Suppression avec coordonnées")
    ]
    
    for cmd_str, description in coordinate_tests:
        result = executor.execute_commands([cmd_str])
        if result['success']:
            print(f"✅ {description}: {cmd_str}")
        else:
            print(f"❌ {description}: {cmd_str} - Erreur: {result['error']}")

def test_error_handling():
    """Test de la gestion d'erreurs moderne"""
    print("\n=== TEST GESTION D'ERREURS MODERNE ===")
    
    executor = CommandExecutor()
    
    error_cases = [
        ("INIT 0x0", "Dimensions invalides"),
        ("EDIT abc [0,0]", "Paramètre non numérique"),
        ("FILL 5 [10,10]", "Coordonnées hors limites"),
        ("INSERT -1 ROWS ABOVE", "Nombre négatif"),
        ("DELETE INVALID [0,0]", "Type invalide"),
        ("UNKNOWN_COMMAND", "Commande inconnue")
    ]
    
    for cmd_str, description in error_cases:
        result = executor.execute_commands([cmd_str])
        if not result['success']:
            print(f"✅ {description}: Erreur correctement détectée")
            print(f"   Commande: {cmd_str}")
            print(f"   Erreur: {result['error']}")
        else:
            print(f"⚠️ {description}: Devrait échouer mais a réussi")
        print()

if __name__ == "__main__":
    test_modern_commands_work()
    test_obsolete_commands_rejected()
    test_no_legacy_methods()
    test_modern_coordinate_format()
    test_error_handling()
    
    print("\n" + "="*60)
    print("🎉 REFACTORISATION TERMINÉE")
    print("✅ Commandes modernes fonctionnelles")
    print("✅ Commandes obsolètes supprimées")
    print("✅ Format de coordonnées conforme à la documentation")
    print("✅ Gestion d'erreurs moderne")
    print("="*60)
