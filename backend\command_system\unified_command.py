"""
Classe UnifiedCommand - Parser centralisé pour toutes les commandes unifiées
"""

import re
from typing import List, Dict, Any, Optional


class UnifiedCommand:
    """
    Classe pour parser et représenter une commande unifiée selon le format FONCTIONNEMENT_GENERAL_COMMANDES_UNIFIEES.md
    """
    
    def __init__(self, action: str, parameters: List[Any] = None, coordinates: List[int] = None, raw_command: str = "", special_selection: Optional[Dict[str, Any]] = None):
        """
        Initialise une commande unifiée
        
        Args:
            action: L'action de la commande (INIT, EDIT, FILL, etc.)
            parameters: Liste des paramètres (couleurs, dimensions, etc.)
            coordinates: Liste des coordonnées [x1,y1,x2,y2,...]
            raw_command: Commande originale sous forme de chaîne
            special_selection: Sélection spéciale (SELECT_COLOR, SELECT_INVERT, etc.)
        """
        self.action = action.upper()
        self.parameters = parameters or []
        self.coordinates = coordinates or []
        self.raw_command = raw_command
        self.special_selection = special_selection  # {"type": "SELECT_COLOR", "params": [4], "coords": [0,0,4,8]}

    @classmethod
    def parse(cls, command: str) -> Optional['UnifiedCommand']:
        """
        Parse une commande unifié et retourne une instance UnifiedCommand
        
        Formats supportés selon FONCTIONNEMENT_GENERAL_COMMANDES_UNIFIEES.md :
        - INIT 10x5 ou INIT 10 5
        - EDIT 7 [0,0]
        - FILL 5 [1,2 8,8] [+8,5 8,8]
        - CLEAR [1,2 8,8] [+8,5 8,8]
        - REPLACE 1,8,3 5 [1,2 8,8]
        - CUT [0,0,2,2]
        - PASTE [4,4]
        - FLIP HORIZONTAL ([1,2 8,8])
        - TRANSFERT {commandes}
        
        Args:
            command: La commande à parser
            
        Returns:
            UnifiedCommand ou None si le parsing échoue
        """
        if not command or not command.strip():
            return None
            
        command = command.strip()
        
        # Rejeter immédiatement les commandes obsolètes avec underscores
        obsolete_actions = ['ROTATE_RIGHT', 'ROTATE_LEFT', 'FLIP_HORIZONTAL', 'FLIP_VERTICAL',
                           'INSERT_ROW', 'INSERT_COL', 'DELETE_ROW', 'DELETE_COL',
                           'INSERT_ROWS', 'INSERT_COLUMNS', 'DELETE_ROWS', 'DELETE_COLUMNS']
        for obsolete in obsolete_actions:
            if command.upper().startswith(obsolete):
                return None
        
        # Gestion des commandes spéciales avec accolades (TRANSFERT)
        if '{' in command and '}' in command:
            return cls._parse_bracketed_command(command)
        
        # Gestion des commandes avec parenthèses (transformations et sélections spéciales)
        if '(' in command and ')' in command:
            return cls._parse_parenthesized_command(command)
        
        # Pattern principal pour les commandes standards
        # ACTION [paramètres] [coordonnées] [coordonnées_multiples]
        # Correction: capturer les paramètres jusqu'au premier crochet ou fin de ligne
        pattern = r'^(\w+)(?:\s+([^\[]*?))?\s*(\[.*)?$'
        match = re.match(pattern, command)
        
        if not match:
            return None
            
        action = match.group(1).upper()
        params_str = match.group(2)
        coords_part = match.group(3)
        
        parameters = []
        if params_str and params_str.strip():
            # Pour FLIP et ROTATE, le premier mot après l'action est un paramètre de direction
            if action in ['FLIP', 'ROTATE']:
                parts = params_str.strip().split()
                if parts:
                    parameters.append(parts[0]) # La direction
                    # Le reste pourrait être d'autres paramètres, si le format évolue
                    remaining_params_str = ' '.join(parts[1:])
                    if remaining_params_str:
                        parameters.extend(cls._parse_parameters(remaining_params_str, action))
            else:
                 parameters = cls._parse_parameters(params_str, action)
        
        # Parser toutes les coordonnées (multiples blocs [...])
        coordinates = []
        if coords_part:
            coord_blocks = re.findall(r'\[([^\]]+)\]', coords_part)
            for block in coord_blocks:
                coordinates.extend(cls._parse_coordinates(block))
        
        return cls(action, parameters, coordinates, command)

    @classmethod
    def _parse_bracketed_command(cls, command: str) -> Optional['UnifiedCommand']:
        """Parse les commandes avec accolades comme TRANSFERT {commandes}"""
        pattern = r'^(\w+)\s*\{([^}]+)\}$'
        match = re.match(pattern, command)
        
        if not match:
            return None
            
        action = match.group(1).upper()
        content = match.group(2).strip()
        
        # Pour TRANSFERT, le contenu des accolades est stocké comme paramètre
        return cls(action, [content], [], command)

    @classmethod
    def _parse_parenthesized_command(cls, command: str) -> Optional['UnifiedCommand']:
        """Parse les commandes avec parenthèses comme FLIP HORIZONTAL ([coords]) ou (SELECT_COLOR ...) ou FILL 3 ([coords] [+coords])"""
        
        # Cas 1: FILL 3 ([1,5 10,5] [10,15 0,15] [0,25 10,25]) - blocs multiples avec parenthèses
        pattern_multi_blocks = r'^(\w+)\s+([^(]+?)\s*\(([^)]+)\)$'
        match_multi = re.match(pattern_multi_blocks, command)
        
        if match_multi:
            action_part = match_multi.group(1).upper()
            params_str = match_multi.group(2).strip()
            blocks_content = match_multi.group(3)
            
            # Vérifier si c'est une commande avec direction (ROTATE RIGHT, FLIP HORIZONTAL, etc.)
            full_action_part = f"{action_part} {params_str}".strip()
            if full_action_part in ['ROTATE RIGHT', 'ROTATE LEFT', 'FLIP HORIZONTAL', 'FLIP VERTICAL']:
                # C'est une commande de transformation, garder l'action avec l'espace
                action = full_action_part
                coordinates = []
                coords_pattern = r'\[([^\]]+)\]'
                coords_matches = re.findall(coords_pattern, blocks_content)
                
                for coords_str in coords_matches:
                    coordinates.extend(cls._parse_coordinates(coords_str))
                
                return cls(action, [], coordinates, command)
            else:
                # C'est vraiment une commande avec paramètres multiples
                action = action_part
                parameters = cls._parse_parameters(params_str, action)
                
                # Extraire tous les blocs [coords] du contenu entre parenthèses
                coordinates = []
                block_pattern = r'\[([^\]]+)\]'
                blocks = re.findall(block_pattern, blocks_content)
                
                return cls(action, parameters, coordinates, command)
        
        # Cas 2: (SELECT_COLOR params [coords]) ou (COLOR params [coords])
        if command.startswith('(') and command.endswith(')'):
            inner = command[1:-1]
            pattern = r'^(\w+(?:_\w+)*)\s+([^[\]]+?)\s*\[([^\]]+)\]$'
            match = re.match(pattern, inner)
            
            if match:
                action = match.group(1).upper()
                params_str = match.group(2)
                coords_str = match.group(3)
                
                parameters = cls._parse_parameters(params_str, action)
                coordinates = cls._parse_coordinates(coords_str)
                
                return cls(action, parameters, coordinates, command)
        
        # Cas 2: COMMAND (SELECT_COLOR params [coords]) - commandes avec sélection spéciale (ancien format)
        # Ces commandes doivent maintenant utiliser le parsing générique
        pattern = r'^(\w+)\s*\((\w+(?:_\w+)*)\s+([^[\]]+?)\s*\[([^\]]+)\]\)$'
        match = re.match(pattern, command)
        
        if match:
            action = match.group(1).upper()
            special_type = match.group(2).upper()
            params_str = match.group(3)
            coords_str = match.group(4)
            
            # Pour les anciens formats avec SELECT_COLOR/SELECT_INVERT, utiliser le parsing générique
            parameters = cls._parse_parameters(params_str, special_type)
            coordinates = cls._parse_coordinates(coords_str)
            
            # Retourner sans special_selection, en s'appuyant sur raw_command pour le parsing générique
            return cls(action, parameters, coordinates, command)
        
        # Cas 2b: COMMAND (COLOR params [coords]) ou COMMAND (INVERT [coords]) - nouveaux modificateurs
        pattern_modifier = r'^(\w+)\s*\((COLOR|INVERT)(?:\s+([^[\]]+?))?\s*\[([^\]]+)\]\)$'
        match_modifier = re.match(pattern_modifier, command)
        
        if match_modifier:
            action = match_modifier.group(1).upper()
            modifier_type = match_modifier.group(2).upper()
            params_str = match_modifier.group(3) if match_modifier.group(3) else ""
            coords_str = match_modifier.group(4)
            
            # Pour les nouveaux modificateurs, on retourne avec raw_command pour le parsing générique
            parameters = []
            if params_str and params_str.strip():
                parameters = cls._parse_parameters(params_str, modifier_type)
            
            coordinates = cls._parse_coordinates(coords_str)
            
            # Retourner sans special_selection, en s'appuyant sur raw_command pour le parsing générique
            return cls(action, parameters, coordinates, command)
        
        # Cas 3: COMMAND (INVERT [coords]) - commandes avec sélection spéciale INVERT
        # Ces commandes doivent maintenant utiliser le parsing générique
        pattern_invert = r'^(\w+)\s*\(INVERT\s*\(([^)]+)\)\)$'
        match_invert = re.match(pattern_invert, command)
        
        if match_invert:
            action = match_invert.group(1).upper()
            coords_content = match_invert.group(2)
            
            # Extraire les coordonnées des crochets
            coords_pattern = r'\[([^\]]+)\]'
            coords_matches = re.findall(coords_pattern, coords_content)
            
            coordinates = []
            for coords_str in coords_matches:
                coordinates.extend(cls._parse_coordinates(coords_str))
            
            # Retourner sans special_selection, en s'appuyant sur raw_command pour le parsing générique
            return cls(action, [], coordinates, command)
        
        # Cas 4: FLIP HORIZONTAL ([coords]) - motifs
        pattern = r'^(\w+(?:\s+\w+)*)\s*\(([^)]+)\)$'
        match = re.match(pattern, command)
        
        if match:
            action_raw = match.group(1).upper()
            action = action_raw  # Garder l'action avec les espaces selon la documentation
            content = match.group(2)
            
            # Extraire les coordonnées de la forme [coords]
            coords_pattern = r'\[([^\]]+)\]'
            coords_matches = re.findall(coords_pattern, content)
            
            coordinates = []
            for coords_str in coords_matches:
                coordinates.extend(cls._parse_coordinates(coords_str))
            
            return cls(action, [], coordinates, command)
        
        return None

    @classmethod
    def _parse_parameters(cls, params_str: str, action: str) -> List[Any]:
        """Parse les paramètres selon le type de commande"""
        if not params_str:
            return []
            
        params_str = params_str.strip()
        
        # Gestion spéciale pour INIT avec format "10x5"
        if action == 'INIT' and 'x' in params_str:
            parts = params_str.split('x')
            if len(parts) == 2:
                try:
                    return [int(parts[0]), int(parts[1])]
                except ValueError:
                    pass
        
        # Gestion des couleurs multiples séparées par des virgules (REPLACE, SELECT_COLOR)
        if ',' in params_str and action in ['REPLACE', 'SELECT_COLOR']:
            # Pour REPLACE: "1,8,3 5" -> source_colors=[1,8,3], target_color=5
            # Pour SELECT_COLOR: "1,2,3" -> colors=[1,2,3]
            parts = params_str.split()
            result = []
            
            for part in parts:
                if ',' in part:
                    # Couleurs multiples
                    colors = []
                    for color in part.split(','):
                        try:
                            colors.append(int(color.strip()))
                        except ValueError:
                            colors.append(color.strip())
                    result.append(colors)
                else:
                    # Paramètre simple
                    try:
                        result.append(int(part))
                    except ValueError:
                        result.append(part)
            
            return result
        
        # Gestion standard: séparer par espaces et convertir si possible
        parts = params_str.split()
        result = []
        
        for part in parts:
            try:
                result.append(int(part))
            except ValueError:
                result.append(part)        
        return result

    @classmethod
    def _parse_coordinates(cls, coords_str: str) -> List[int]:
        """Parse les coordonnées de différents formats"""
        if not coords_str:
            return []
            
        coords_str = coords_str.strip()
        coordinates = []
        
        # Format mixte "x,y z,w" ou "x,y z w" (ex: "0,0 4,4")
        if ',' in coords_str and ' ' in coords_str:
            # Remplacer tous les espaces par des virgules pour normaliser
            normalized = coords_str.replace(' ', ',')
            for coord in normalized.split(','):
                coord = coord.strip()
                if coord:  # Éviter les chaînes vides
                    try:
                        coordinates.append(int(coord))
                    except ValueError:
                        pass
        
        # Format "x,y" ou "x1,y1,x2,y2" (seulement des virgules)
        elif ',' in coords_str:
            for coord in coords_str.split(','):
                try:
                    coordinates.append(int(coord.strip()))
                except ValueError:
                    pass
        
        # Format "x y" ou "x1 y1 x2 y2" (seulement des espaces)
        elif ' ' in coords_str:
            for coord in coords_str.split():
                try:
                    coordinates.append(int(coord.strip()))
                except ValueError:
                    pass
        
        # Format simple "x" ou "xy"
        else:
            try:
                coordinates.append(int(coords_str))
            except ValueError:
                pass
        
        return coordinates

    def validate_syntax(self) -> bool:
        """
        Valide la syntaxe de la commande selon les règles du format unifié
        
        Returns:
            bool: True si la syntaxe est valide
        """
        if not self.action:
            return False
        
        # Validation spécifique par type de commande
        if self.action == 'INIT':
            return len(self.parameters) == 2 and len(self.coordinates) == 0
        
        elif self.action == 'EDIT':
            return len(self.parameters) == 1 and len(self.coordinates) == 2
        elif self.action in ['FILL', 'SURROUND']:
            # Accepter soit une sélection spéciale, soit des coordonnées normales
            if self.special_selection:
                return len(self.parameters) == 1  # Juste la valeur de remplissage
            return len(self.parameters) == 1 and len(self.coordinates) >= 2
        
        elif self.action == 'CLEAR':
            return len(self.parameters) == 0 and len(self.coordinates) >= 2
        
        elif self.action == 'REPLACE':
            # REPLACE source_colors target_color [coordinates]
            return len(self.parameters) >= 2 and len(self.coordinates) >= 2
        
        elif self.action == 'CUT':
            # CUT normal: CUT [x1,y1,x2,y2] ou CUT avec sélection spéciale: CUT (SELECT_COLOR ...)
            if self.special_selection:
                return True  # Les sélections spéciales sont valides
            return len(self.parameters) == 0 and len(self.coordinates) == 4
        
        elif self.action == 'PASTE':
            return len(self.parameters) == 0 and len(self.coordinates) == 2
        
        elif self.action in ['COPY', 'FLIP HORIZONTAL', 'FLIP VERTICAL', 'ROTATE LEFT', 'ROTATE RIGHT']:
            # COPY normal: COPY [x1,y1,x2,y2] ou COPY avec sélection spéciale: COPY (SELECT_COLOR ...)
            if self.special_selection:
                return True  # Les sélections spéciales sont valides
            return len(self.coordinates) >= 2
        
        elif self.action in ['INSERT']:
            # INSERT nombre ROWS/COLUMNS BEFORE/AFTER/ABOVE/BELOW [coordinates]
            return len(self.parameters) >= 3 and len(self.coordinates) >= 2
        
        elif self.action == 'DELETE':
            # DELETE ROWS/COLUMNS [coordinates]
            return len(self.parameters) >= 1 and len(self.coordinates) >= 2
        
        elif self.action == 'EXTRACT':
            return len(self.parameters) == 0 and len(self.coordinates) == 4
        
        elif self.action in ['SELECT_INVERT', 'SELECT_COLOR']:
            return len(self.coordinates) >= 2
        
        elif self.action == 'TRANSFERT':
            return len(self.parameters) == 1  # Contenu des accolades
        
        elif self.action in ['END', 'SELECT_RELEASE']:
            return len(self.parameters) == 0 and len(self.coordinates) == 0
        
        elif self.action == 'RESIZE':
            return len(self.parameters) == 1  # Format "9x9"
        
        # Pour les nouvelles commandes ou cas non prévus
        return True

    def to_dict(self) -> Dict[str, Any]:
        """
        Convertit la commande en dictionnaire
        
        Returns:
            Dict avec les clés 'action', 'parameters', 'coordinates', 'raw_command'
        """
        return {
            'action': self.action,
            'parameters': self.parameters,
            'coordinates': self.coordinates,
            'raw_command': self.raw_command
        }

    def __str__(self) -> str:
        """Représentation en chaîne de la commande"""
        if self.raw_command:
            return self.raw_command
        
        # Reconstruction de la commande si nécessaire
        parts = [self.action]
        
        if self.parameters:
            parts.extend([str(p) for p in self.parameters])
        
        if self.coordinates:
            # Regrouper les coordonnées par paires pour les rectangles
            if len(self.coordinates) >= 4:
                coords_str = f"[{self.coordinates[0]},{self.coordinates[1]} {self.coordinates[2]},{self.coordinates[3]}]"
            elif len(self.coordinates) >= 2:
                coords_str = f"[{self.coordinates[0]},{self.coordinates[1]}]"
            else:
                coords_str = f"[{self.coordinates[0]}]"
            parts.append(coords_str)
        
        return " ".join(parts)

    def __repr__(self) -> str:
        """Représentation pour debug"""
        return f"UnifiedCommand(action='{self.action}', parameters={self.parameters}, coordinates={self.coordinates})"
