#!/usr/bin/env python3
"""
Debug des blocs de coordonnées
"""

import sys
import os

# Ajouter le chemin du backend
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

try:
    from command_system.unified_command import UnifiedCommand
    from command_system.command_executor import CommandExecutor
except ImportError as e:
    print(f"Erreur d'import: {e}")
    sys.exit(1)

def test_coordinate_blocks():
    """Test du parsing des blocs de coordonnées"""
    print("=== TEST BLOCS DE COORDONNÉES ===")
    
    test_commands = [
        "FILL 5 [1,2 3,4]",
        "FILL 5 ([1,2 3,4] [5,6] [7,8 9,10])",
        "CLEAR [0,0 2,2]",
        "COPY [3,0 5,2]",
        "PASTE [6,0]",
        "REPLACE 1 2 [0,0 8,2]"
    ]
    
    for cmd_str in test_commands:
        print(f"\nCommande: {cmd_str}")
        
        parsed = UnifiedCommand.parse(cmd_str)
        if parsed:
            print(f"✅ Parsing réussi")
            print(f"   Action: {parsed.action}")
            print(f"   Paramètres: {parsed.parameters}")
            print(f"   Coordonnées (liste plate): {parsed.coordinates}")
            print(f"   Blocs de coordonnées: {parsed.coordinate_blocks}")
        else:
            print(f"❌ Échec du parsing")

def test_scenario_007bbfb7():
    """Test spécifique du scénario 007bbfb7"""
    print("\n=== TEST SCÉNARIO 007bbfb7 ===")
    
    commands = [
        "TRANSFERT {INIT 3x3; EDIT 7 [0,0]; EDIT 7 [0,2]; EDIT 7 [1,0]; EDIT 7 [1,2]; EDIT 7 [2,0]; EDIT 7 [2,1]}",
        "RESIZE 9x9",
        "COPY [0,0 2,2]",
        "PASTE [3,0]"
    ]
    
    executor = CommandExecutor()
    
    for i, cmd_str in enumerate(commands, 1):
        print(f"\n{i}. {cmd_str}")
        
        # Test du parsing
        parsed = UnifiedCommand.parse(cmd_str)
        if not parsed:
            print(f"   ❌ Échec du parsing")
            break
            
        print(f"   ✅ Parsing réussi: {parsed.action}")
        if parsed.coordinate_blocks:
            print(f"   Blocs: {parsed.coordinate_blocks}")
        
        # Test de l'exécution
        try:
            result = executor._execute_unified_command(parsed)
            if result:
                print(f"   ✅ Exécution réussie")
                if hasattr(executor, 'grid') and executor.grid is not None:
                    print(f"   Grille: {executor.height}x{executor.width}")
            else:
                print(f"   ❌ Échec de l'exécution")
                if hasattr(executor, 'error'):
                    print(f"   Erreur: {executor.error}")
                break
        except Exception as e:
            print(f"   ❌ Exception: {e}")
            import traceback
            traceback.print_exc()
            break

def test_complex_coordinates():
    """Test des coordonnées complexes avec parenthèses"""
    print("\n=== TEST COORDONNÉES COMPLEXES ===")
    
    complex_commands = [
        "FILL 5 ([1,2 3,4] [5,6] [7,8 9,10])",
        "CLEAR (INVERT ([0,0 2,2]))",
        "REPLACE 1 2 (COLOR 3,4 ([1,1 3,3]))"
    ]
    
    for cmd_str in complex_commands:
        print(f"\nCommande: {cmd_str}")
        
        parsed = UnifiedCommand.parse(cmd_str)
        if parsed:
            print(f"✅ Parsing réussi")
            print(f"   Blocs: {parsed.coordinate_blocks}")
        else:
            print(f"❌ Échec du parsing")

if __name__ == "__main__":
    test_coordinate_blocks()
    test_scenario_007bbfb7()
    test_complex_coordinates()
