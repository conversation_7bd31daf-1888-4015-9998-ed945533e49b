ResolutionGrid.tsx:266 [ResolutionGrid] Mouse up - finalizing selection
CommandEditor.tsx:338 🔍 Diagnostic currentTask: {currentTask: {…}, hasCurrentTask: true, currentTaskId: '46442a0e', currentTaskType: 'object', currentTestPairIndex: 0, …}
CommandEditor.tsx:348 🔍 Diagnostic useTask complet: {currentTask: {…}, currentTestPairIndex: 0, taskKeys: Array(3), taskTests: 3}
CommandEditor.tsx:370 ✅ Tâche valide détectée: {hasId: true, trainCount: 3, testCount: 1}
CommandEditor.tsx:377 🔍 Démarrage de la validation robuste côté backend...
CommandEditor.tsx:378 📋 Commandes à valider: (12) ['TRANSFERT {INIT 3x3; EDIT 1 [0,0]; EDIT 4 [0,1]; E… [1,2]; EDIT 9 [2,0]; EDIT 1 [2,1]; EDIT 9 [2,2]}', 'RESIZE 6x6', 'COPY [0,0 2,2]', 'ROTATE RIGHT [0,3 2,5]', 'PASTE [0,3]', 'COPY [0,3 2,5]', 'ROTATE RIGHT [3,3 5,5]', 'PASTE [3,3]', 'COPY [3,3 5,5]', 'ROTATE RIGHT [3,0 5,2]', 'PASTE [3,0]', 'END']
CommandEditor.tsx:381 🎯 Mode validation optimisée: Validation complète avec task_id et test_index
CommandEditor.tsx:390 📤 Requête de validation: {scenario_content: 'TRANSFERT {INIT 3x3; EDIT 1 [0,0]; EDIT 4 [0,1]; E… [3,3 5,5]\nROTATE RIGHT [3,0 5,2]\nPASTE [3,0]\nEND', task_id: '46442a0e', subset: 'training', test_index: 0}
CommandEditor.tsx:391 🎯 Validation optimisée complète activée pour la tâche: 46442a0e
commandValidationApi.ts:120 🔍 DEBUG: result: {valid: false, execution_success: true, grid_matches_expected: false, stage: 'comparison', command_stats: {…}, …}
commandValidationApi.ts:122 🔍 DEBUG: debug_info: {original_commands: Array(12), expanded_commands: Array(21), backend_commands: Array(21), execution_result: {…}, comparison_result: {…}, …}
commandValidationApi.ts:125 🔍 DEBUG: comparison_result: {matches: false, error: '4 cellules différentes', generated_grid: Array(6), expected_grid: Array(6), dimensions_match: true, …}
commandValidationApi.ts:127 ✅ DEBUG: Ajout de generated_grid à l'erreur
commandValidationApi.ts:131 ✅ DEBUG: Ajout de expected_grid à l'erreur
CommandEditor.tsx:419 ❌ Erreurs de validation détectées: [{…}]
overrideMethod @ hook.js:608
handleValidateCommands @ CommandEditor.tsx:419
await in handleValidateCommands
executeDispatch @ react-dom_client.js?v=112e557a:11736
runWithFiberInDEV @ react-dom_client.js?v=112e557a:1485
processDispatchQueue @ react-dom_client.js?v=112e557a:11772
(anonymous) @ react-dom_client.js?v=112e557a:12182
batchedUpdates$1 @ react-dom_client.js?v=112e557a:2628
dispatchEventForPluginEventSystem @ react-dom_client.js?v=112e557a:11877
dispatchEvent @ react-dom_client.js?v=112e557a:14792
dispatchDiscreteEvent @ react-dom_client.js?v=112e557a:14773
CommandEditor.tsx:430 🔍 Structure des erreurs:
CommandEditor.tsx:432 
--- Erreur 1 ---
CommandEditor.tsx:433 Command Index: 0
CommandEditor.tsx:434 Command: 
CommandEditor.tsx:435 Error Type: execution
CommandEditor.tsx:436 Message: 4 cellules différentes
CommandEditor.tsx:439 
🔢 Generated Grid:
CommandEditor.tsx:440 1 4 1 9 4 1
4 9 4 1 9 4
9 1 9 9 4 1
9 1 9 9 1 9
4 9 4 4 9 4
1 4 1 1 4 1
CommandEditor.tsx:444 
🎯 Expected Grid:
CommandEditor.tsx:445 1 4 1 9 4 1
4 9 4 1 9 4
9 1 9 9 4 1
1 4 9 9 1 9
4 9 1 4 9 4
1 4 9 1 4 1
CommandEditor.tsx:455 🔍 Vérification erreur: {commandIndex: 0, command: '', errorType: 'execution', message: '4 cellules différentes', suggestions: Array(0), …}
CommandEditor.tsx:456 🔍 generated_grid: (6) [Array(6), Array(6), Array(6), Array(6), Array(6), Array(6)]
CommandEditor.tsx:457 🔍 expected_grid: (6) [Array(6), Array(6), Array(6), Array(6), Array(6), Array(6)]
CommandEditor.tsx:461 🔍 hasGridErrors: true
CommandEditor.tsx:462 🔍 showGridDiff avant: false
CommandEditor.tsx:465 ✅ Activation du GridDiffViewer
CommandEditor.tsx:473 Ligne 1: 
              Type: execution
              Message: 4 cellules différentes
              Suggestions: 
overrideMethod @ hook.js:608
(anonymous) @ CommandEditor.tsx:473
handleValidateCommands @ CommandEditor.tsx:472
await in handleValidateCommands
executeDispatch @ react-dom_client.js?v=112e557a:11736
runWithFiberInDEV @ react-dom_client.js?v=112e557a:1485
processDispatchQueue @ react-dom_client.js?v=112e557a:11772
(anonymous) @ react-dom_client.js?v=112e557a:12182
batchedUpdates$1 @ react-dom_client.js?v=112e557a:2628
dispatchEventForPluginEventSystem @ react-dom_client.js?v=112e557a:11877
dispatchEvent @ react-dom_client.js?v=112e557a:14792
dispatchDiscreteEvent @ react-dom_client.js?v=112e557a:14773
GridDiffViewer.tsx:33 🔍 GridDiffViewer - Erreurs reçues: [{…}]
GridDiffViewer.tsx:39 🔍 Erreur: 0 hasDirectGrids: (6) [Array(6), Array(6), Array(6), Array(6), Array(6), Array(6)] hasGridComparison: undefined
GridDiffViewer.tsx:43 🔍 GridDiffViewer - Erreurs avec grilles: 1
GridDiffViewer.tsx:135 🔍 Rendu erreur: 0 generated_grid: (6) [Array(6), Array(6), Array(6), Array(6), Array(6), Array(6)] expected_grid: (6) [Array(6), Array(6), Array(6), Array(6), Array(6), Array(6)]
GridDiffViewer.tsx:33 🔍 GridDiffViewer - Erreurs reçues: [{…}]
GridDiffViewer.tsx:39 🔍 Erreur: 0 hasDirectGrids: (6) [Array(6), Array(6), Array(6), Array(6), Array(6), Array(6)] hasGridComparison: undefined
GridDiffViewer.tsx:43 🔍 GridDiffViewer - Erreurs avec grilles: 1
GridDiffViewer.tsx:135 🔍 Rendu erreur: 0 generated_grid: (6) [Array(6), Array(6), Array(6), Array(6), Array(6), Array(6)] expected_grid: (6) [Array(6), Array(6), Array(6), Array(6), Array(6), Array(6)]
