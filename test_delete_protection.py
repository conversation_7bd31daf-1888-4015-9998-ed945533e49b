#!/usr/bin/env python3
"""
Test de la protection contre la suppression de toutes les lignes/colonnes
"""

import sys
import os

# Ajouter le chemin du backend
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

try:
    from command_system.command_executor import CommandExecutor
except ImportError as e:
    print(f"Erreur d'import: {e}")
    sys.exit(1)

def test_delete_protection():
    """Test de la protection contre la suppression complète"""
    print("=== TEST PROTECTION SUPPRESSION ===")
    
    executor = CommandExecutor()
    
    # Test 1: Essayer de supprimer la seule ligne
    print("\n1. Tentative suppression seule ligne")
    result = executor.execute_commands([
        "INIT 1x3",
        "EDIT 5 [0,0]",
        "DELETE ROWS [0,0 0,2]"
    ])
    print(f"Succès: {result['success']}")
    print(f"Erreur: {result['error']}")
    
    # Test 2: Essayer de supprimer la seule colonne
    print("\n2. Tentative suppression seule colonne")
    result = executor.execute_commands([
        "INIT 3x1",
        "EDIT 5 [0,0]",
        "DELETE COLUMNS [0,0 2,0]"
    ])
    print(f"Succès: {result['success']}")
    print(f"Erreur: {result['error']}")
    
    # Test 3: Essayer de supprimer toutes les lignes d'une grille 3x3
    print("\n3. Tentative suppression toutes les lignes")
    result = executor.execute_commands([
        "INIT 3x3",
        "EDIT 1 [0,0]", "EDIT 2 [1,1]", "EDIT 3 [2,2]",
        "DELETE ROWS [0,0 2,2]"  # Supprimer lignes 0, 1, 2
    ])
    print(f"Succès: {result['success']}")
    print(f"Erreur: {result['error']}")
    
    # Test 4: Essayer de supprimer toutes les colonnes d'une grille 3x3
    print("\n4. Tentative suppression toutes les colonnes")
    result = executor.execute_commands([
        "INIT 3x3",
        "EDIT 1 [0,0]", "EDIT 2 [1,1]", "EDIT 3 [2,2]",
        "DELETE COLUMNS [0,0 2,2]"  # Supprimer colonnes 0, 1, 2
    ])
    print(f"Succès: {result['success']}")
    print(f"Erreur: {result['error']}")
    
    # Test 5: Suppression valide (laisser au moins une ligne/colonne)
    print("\n5. Suppression valide (garder une ligne)")
    result = executor.execute_commands([
        "INIT 3x3",
        "EDIT 1 [0,0]", "EDIT 2 [1,1]", "EDIT 3 [2,2]",
        "DELETE ROWS [0,0 1,2]"  # Supprimer lignes 0, 1 (garder ligne 2)
    ])
    print(f"Succès: {result['success']}")
    if result['error']:
        print(f"Erreur: {result['error']}")
    else:
        print("Grille résultante:")
        for row in result['grid']:
            print("  " + " ".join(str(cell) for cell in row))

if __name__ == "__main__":
    test_delete_protection()
    print("\n=== TEST TERMINÉ ===")
