#!/usr/bin/env python3
"""
Test du CommandValidator refactorisé
"""

import sys
import os

# Ajouter le chemin du backend
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

try:
    from command_system.command_validator import CommandValidator
except ImportError as e:
    print(f"Erreur d'import: {e}")
    sys.exit(1)

def test_modern_commands_validation():
    """Test de validation des commandes modernes"""
    print("=== TEST VALIDATION COMMANDES MODERNES ===")
    
    validator = CommandValidator()
    
    modern_commands = [
        "INIT 3x3",
        "EDIT 7 [0,0]",
        "FILL 5 [1,1 2,2]",
        "CLEAR [0,0 1,1]",
        "INSERT 1 ROWS ABOVE",
        "DELETE COLUMNS [0,0 2,0]",
        "FLIP HORIZONTAL ([1,1 2,2])",
        "ROTATE LEFT ([0,0 1,1])",
        "TRANSFERT {INIT 3x3; EDIT 7 [0,0]}",
        "END"
    ]
    
    result = validator.validate_commands(modern_commands)
    
    print(f"Validation globale: {'✅ Succès' if result['valid'] else '❌ Échec'}")
    print(f"Commandes valides: {len(result['valid_commands'])}/{len(modern_commands)}")
    
    if result['errors']:
        print("Erreurs détectées:")
        for error in result['errors']:
            print(f"  Ligne {error['line']}: {error['error']}")
    else:
        print("✅ Toutes les commandes modernes sont valides")

def test_obsolete_commands_rejection():
    """Test de rejet des commandes obsolètes"""
    print("\n=== TEST REJET COMMANDES OBSOLÈTES ===")
    
    validator = CommandValidator()
    
    obsolete_commands = [
        "RESIZE 5x5",           # Obsolète
        "SELECT_RELEASE",       # Obsolète
        "EDITS {EDIT 1 [0,0]}", # Obsolète
        "ROTATE_RIGHT [0,0 2,2]", # Format obsolète
        "FLIP_HORIZONTAL [1,1 3,3]", # Format obsolète
        "INSERT_ROW 1",         # Format obsolète
        "DELETE_COL 2",         # Format obsolète
        "PROPOSE",              # Obsolète
        "VALIDATE"              # Obsolète
    ]
    
    result = validator.validate_commands(obsolete_commands)
    
    print(f"Validation globale: {'✅ Rejeté' if not result['valid'] else '⚠️ Accepté'}")
    print(f"Commandes rejetées: {len(result['errors'])}/{len(obsolete_commands)}")
    
    if result['errors']:
        print("Commandes obsolètes correctement rejetées:")
        for error in result['errors']:
            print(f"  Ligne {error['line']}: {error['command']} - {error['error']}")
    else:
        print("⚠️ Aucune commande obsolète rejetée")

def test_single_command_validation():
    """Test de validation de commandes individuelles"""
    print("\n=== TEST VALIDATION COMMANDES INDIVIDUELLES ===")
    
    validator = CommandValidator()
    
    test_cases = [
        ("INIT 3x3", True, "Commande moderne valide"),
        ("EDIT 5 [2,3]", True, "Format coordonnées moderne"),
        ("RESIZE 5x5", False, "Commande obsolète"),
        ("INVALID_COMMAND", False, "Commande inconnue"),
        ("EDIT abc [0,0]", True, "Syntaxe valide mais paramètre incorrect"),
        ("", True, "Ligne vide (ignorée)"),
        ("# Commentaire", True, "Commentaire (ignoré)")
    ]
    
    for command, expected_valid, description in test_cases:
        result = validator.validate_single_command(command)
        actual_valid = result['valid']
        
        status = "✅" if actual_valid == expected_valid else "❌"
        print(f"{status} {description}: '{command}' - Valide: {actual_valid}")
        
        if result['error']:
            print(f"    Erreur: {result['error']}")

def test_command_suggestions():
    """Test des suggestions de correction"""
    print("\n=== TEST SUGGESTIONS DE CORRECTION ===")
    
    validator = CommandValidator()
    
    incorrect_commands = [
        "EDIT 0 0 5",           # Format ancien -> EDIT 5 [0,0]
        "INIT 3 3",             # Format ancien -> INIT 3x3
        "UNKNOWN_ACTION",       # Action inconnue
    ]
    
    for command in incorrect_commands:
        suggestions = validator.suggest_corrections(command)
        print(f"Commande: '{command}'")
        if suggestions:
            print(f"  Suggestions:")
            for suggestion in suggestions:
                print(f"    - {suggestion}")
        else:
            print(f"  Aucune suggestion disponible")
        print()

def test_coordinate_format_validation():
    """Test de validation du format des coordonnées"""
    print("\n=== TEST VALIDATION FORMAT COORDONNÉES ===")
    
    validator = CommandValidator()
    
    coordinate_tests = [
        ("EDIT 5 [2,3]", "Cellule individuelle [ligne,colonne]"),
        ("FILL 7 [0,0 2,2]", "Zone rectangulaire [ligne1,colonne1 ligne2,colonne2]"),
        ("CLEAR [1,1 3,3]", "Zone rectangulaire pour effacement"),
        ("COPY [0,0 4,4]", "Zone rectangulaire pour copie"),
        ("FILL 5 [1,2 3,4] [6,7 8,9]", "Blocs multiples de coordonnées")
    ]
    
    for command, description in coordinate_tests:
        result = validator.validate_single_command(command)
        status = "✅" if result['valid'] else "❌"
        print(f"{status} {description}: {command}")
        
        if result['unified_command']:
            coords = result['unified_command'].coordinates
            print(f"    Coordonnées parsées: {coords}")
        
        if result['error']:
            print(f"    Erreur: {result['error']}")

if __name__ == "__main__":
    test_modern_commands_validation()
    test_obsolete_commands_rejection()
    test_single_command_validation()
    test_command_suggestions()
    test_coordinate_format_validation()
    
    print("\n" + "="*60)
    print("🎉 VALIDATION COMMANDVALIDATOR REFACTORISÉ")
    print("✅ Commandes modernes validées correctement")
    print("✅ Commandes obsolètes rejetées")
    print("✅ Format de coordonnées conforme")
    print("✅ Suggestions de correction fonctionnelles")
    print("="*60)
